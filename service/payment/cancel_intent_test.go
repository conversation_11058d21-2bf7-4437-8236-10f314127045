package payment

import (
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestCanBeCancelled(t *testing.T) {
	tests := []struct {
		name     string
		status   domain.PaymentIntentStatus
		expected bool
	}{
		{
			name:     "pending status can be cancelled",
			status:   domain.PaymentIntentStatusPending,
			expected: true,
		},
		{
			name:     "success status cannot be cancelled",
			status:   domain.PaymentIntentStatusSuccess,
			expected: false,
		},
		{
			name:     "expired status cannot be cancelled",
			status:   domain.PaymentIntentStatusExpired,
			expected: false,
		},
		{
			name:     "insufficient_refunded status cannot be cancelled",
			status:   domain.PaymentIntentStatusInsufficientRefunded,
			expected: false,
		},
		{
			name:     "insufficient_not_refunded status cannot be cancelled",
			status:   domain.PaymentIntentStatusInsufficientNotRefunded,
			expected: false,
		},
		{
			name:     "cancelled status cannot be cancelled again",
			status:   domain.PaymentIntentStatusCancelled,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := canBeCancelled(tt.status)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Note: Full integration tests for CancelIntent require database setup
// and are better suited for integration test suites

func TestCancelIntentResponse(t *testing.T) {
	now := time.Now()

	response := &CancelIntentResponse{
		IntentID:    "test-intent-123",
		Status:      "cancelled",
		CancelledAt: now,
	}

	assert.Equal(t, "test-intent-123", response.IntentID)
	assert.Equal(t, "cancelled", response.Status)
	assert.Equal(t, now, response.CancelledAt)
}

func TestCancelIntentParams(t *testing.T) {
	params := CancelIntentParams{
		IntentID: "test-intent-456",
		ClientID: "test-client-789",
	}

	assert.Equal(t, "test-intent-456", params.IntentID)
	assert.Equal(t, "test-client-789", params.ClientID)
}
