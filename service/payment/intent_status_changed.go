package payment

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/payment_item"
)

// PreparedEmailData holds the subject and parameters for an email.
// This struct is defined locally for use within this package.
type PreparedEmailData struct {
	Subject    string
	Parameters map[string]any
}

// IntentStatusChanged handles the status change of a payment intent
func IntentStatusChanged(ctx context.Context, intent *domain.PaymentIntent) error {
	// Fire and forget - run the callback asynchronously
	tracing.AsyncOp(ctx, "payment.IntentStatusChanged.Callback", func(asyncCtx context.Context) {
		err := sendCallback(asyncCtx, intent)
		if err != nil {
			kglog.ErrorWithDataCtx(asyncCtx, "[payment.callback] callback failed", map[string]any{
				"error":     err.Error(),
				"intent_id": intent.ID,
			})
		}
	})
	// Send status update emails in a new goroutine
	tracing.AsyncOp(ctx, "payment.IntentStatusChanged.SendEmails", func(emailCtx context.Context) {
		kglog.InfoWithDataCtx(emailCtx, "[payment.email] Sending emails", map[string]any{
			"intent_id":        intent.ID,
			"status":           string(intent.Status),
			"send_to_owner":    shouldSendEmailToOwner(intent.Status),
			"send_to_customer": shouldSendEmailToCustomer(intent.Status),
		})
		sendPaymentStatusEmails(emailCtx, intent)
	})

	return nil
}

// shouldSendEmailToOwner determines if an email should be sent to the org owner based on intent status.
// Emails are sent for Succeeded, InsufficientFunds (InsufficientRefunded, InsufficientNotRefunded), or Cancelled statuses.
func shouldSendEmailToOwner(status domain.PaymentIntentStatus) bool {
	switch status {
	case domain.PaymentIntentStatusSuccess,
		domain.PaymentIntentStatusInsufficientRefunded,
		domain.PaymentIntentStatusInsufficientNotRefunded,
		domain.PaymentIntentStatusCancelled:
		return true
	default:
		kglog.DebugWithData("[payment.email] Not sending email to owner for status", map[string]any{
			"status": string(status),
		})
		return false
	}
}

// shouldSendEmailToCustomer determines if an email should be sent to the customer based on intent status.
// Emails are sent for any status that is NOT Pending or Expired.
func shouldSendEmailToCustomer(status domain.PaymentIntentStatus) bool {
	return status != domain.PaymentIntentStatusPending && status != domain.PaymentIntentStatusExpired
}

// sendPaymentStatusEmails sends notification emails based on new conditions.
func sendPaymentStatusEmails(ctx context.Context, intent *domain.PaymentIntent) {
	spanCtx, span := tracing.Start(ctx, "payment.sendPaymentStatusEmails")
	defer span.End()

	kglog.InfoWithDataCtx(spanCtx, "[payment.email] Starting email process", map[string]any{
		"intent_id":        intent.ID,
		"status":           string(intent.Status),
		"send_to_owner":    shouldSendEmailToOwner(intent.Status),
		"send_to_customer": shouldSendEmailToCustomer(intent.Status),
	})

	if !shouldSendEmailToOwner(intent.Status) && !shouldSendEmailToCustomer(intent.Status) {
		kglog.InfoWithDataCtx(spanCtx, "[payment.email] No emails to send", map[string]any{
			"intent_id": intent.ID,
			"status":    string(intent.Status),
		})
		return
	}

	kglog.DebugWithDataCtx(spanCtx, "[payment.email] Should send emails", map[string]any{
		"intent_id": intent.ID,
		"status":    string(intent.Status),
	})

	oauthApp, appErr := application.GetOAuthApplication(spanCtx, intent.ClientID)
	rawAppName := "Payment Service" // Default app name
	isGenericAppName := true
	if appErr != nil {
		kglog.WarningWithDataCtx(spanCtx, "[payment.email] Failed to get OAuthApplication, using default app name", map[string]any{
			"error":     appErr.Error.Error(),
			"client_id": intent.ClientID,
		})
	} else if oauthApp != nil && oauthApp.Name != "" {
		rawAppName = oauthApp.Name
		isGenericAppName = false
	}

	kglog.DebugWithDataCtx(spanCtx, "[payment.email] Got OAuthApplication", map[string]any{
		"intent_id": intent.ID,
		"status":    string(intent.Status),
		"app_name":  rawAppName,
	})

	// 1. Handle email to organization owner(s)
	if shouldSendEmailToOwner(intent.Status) {
		kglog.DebugWithDataCtx(spanCtx, "[payment.email] Should send email to owner", map[string]any{
			"intent_id": intent.ID,
			"status":    string(intent.Status),
		})

		// Skip owner emails if studioOrgRepo is nil
		if studioOrgRepo == nil {
			kglog.ErrorWithDataCtx(spanCtx, "[payment.email] studioOrgRepo is not initialized, skipping owner emails", map[string]any{
				"intent_id": intent.ID,
				"status":    string(intent.Status),
			})
		} else {
			orgID, kgErr := application.GetApplicationOrgId(spanCtx, intent.ClientID)
			if kgErr != nil {
				kglog.ErrorWithDataCtx(spanCtx, "[payment.email] Failed to get organization ID for client (owner email)", map[string]any{
					"error":     kgErr.Error.Error(),
					"client_id": intent.ClientID,
				})
			} else {
				kglog.DebugWithDataCtx(spanCtx, "[payment.email] Got organization ID", map[string]any{
					"intent_id": intent.ID,
					"status":    string(intent.Status),
					"org_id":    orgID,
				})

				// Use GetStudioAdmin to get the organization owner directly
				owner, kgErrDb := studioOrgRepo.GetStudioAdmin(spanCtx, orgID)
				kglog.DebugWithDataCtx(spanCtx, "[payment.email] Got organization admin", map[string]any{
					"intent_id": intent.ID,
					"status":    string(intent.Status),
					"org_id":    orgID,
					"owner":     owner,
				})
				if kgErrDb != nil {
					kglog.ErrorWithDataCtx(spanCtx, "[payment.email] Failed to get organization admin", map[string]any{
						"error":  kgErrDb.Error.Error(),
						"org_id": orgID,
					})
				} else if owner != nil && owner.Email != "" && owner.Status == domain.StudioUserStatusActive {
					kglog.DebugWithDataCtx(spanCtx, "[payment.email] Found organization admin", map[string]any{
						"owner_name":  owner.Name,
						"owner_email": owner.Email,
					})

					successMessage := extractSuccessMessageFromOrderData(spanCtx, intent)
					preparedData, err := prepareEmailParams(spanCtx, intent, oauthApp, rawAppName, isGenericAppName, owner.Name, true, successMessage)
					if err != nil {
						kglog.ErrorWithDataCtx(spanCtx, "[payment.email] Failed to prepare email params for owner", map[string]any{
							"error":       err.Error(),
							"intent_id":   intent.ID,
							"owner_email": owner.Email,
						})
					} else {
						sendEmailToRecipient(spanCtx, owner.Email, preparedData.Subject, preparedData.Parameters)
					}
				} else {
					kglog.WarningWithDataCtx(spanCtx, "[payment.email] Organization admin not found or not active", map[string]any{
						"org_id":       orgID,
						"admin_exists": owner != nil,
					})
				}
			}
		}
	}

	// 2. Handle email to customer specified in OrderData
	if shouldSendEmailToCustomer(intent.Status) && intent.OrderData != nil {
		kglog.DebugWithDataCtx(spanCtx, "[payment.email] Should send email to customer", map[string]any{
			"intent_id": intent.ID,
			"status":    string(intent.Status),
		})
		if customerEmail, ok := intent.OrderData["email"].(string); ok && util.IsEmailValid(customerEmail) {
			kglog.DebugWithDataCtx(spanCtx, "[payment.email] Found customer email", map[string]any{
				"intent_id": intent.ID,
				"status":    string(intent.Status),
				"email":     customerEmail,
			})
			customerRecipientName := "Customer" // Default
			if name, ok := intent.OrderData["name"].(string); ok && name != "" {
				customerRecipientName = name
			} else if firstName, ok := intent.OrderData["firstName"].(string); ok && firstName != "" {
				customerRecipientName = firstName
				if lastName, ok := intent.OrderData["lastName"].(string); ok && lastName != "" {
					customerRecipientName += " " + lastName
				}
			}

			customerSuccessMessage := extractSuccessMessageFromOrderData(spanCtx, intent)
			preparedData, err := prepareEmailParams(spanCtx, intent, oauthApp, rawAppName, isGenericAppName, customerRecipientName, false, customerSuccessMessage)
			if err != nil {
				kglog.ErrorWithDataCtx(spanCtx, "[payment.email] Failed to prepare email params for customer", map[string]any{
					"error":          err.Error(),
					"intent_id":      intent.ID,
					"customer_email": customerEmail,
				})
				// Decide if we should return or continue if customer email prep fails
			} else {
				sendEmailToRecipient(spanCtx, customerEmail, preparedData.Subject, preparedData.Parameters)
			}
		}
	}
}

// extractSuccessMessageFromOrderData safely extracts the success message from the first product in OrderData
func extractSuccessMessageFromOrderData(ctx context.Context, intent *domain.PaymentIntent) string {
	if intent.OrderData == nil {
		kglog.DebugWithDataCtx(ctx, "[payment.email] OrderData is nil, cannot extract success message", map[string]any{
			"intent_id": intent.ID,
		})
		return ""
	}

	productsData, ok := intent.OrderData["products"]
	if !ok {
		kglog.WarningWithDataCtx(ctx, "[payment.email] No products field in OrderData", map[string]any{
			"intent_id": intent.ID,
		})
		return ""
	}

	productsSlice, ok := productsData.([]interface{})
	if !ok {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] Products field is not a slice", map[string]any{
			"intent_id":     intent.ID,
			"products_type": fmt.Sprintf("%T", productsData),
		})
		return ""
	}

	if len(productsSlice) == 0 {
		kglog.WarningWithDataCtx(ctx, "[payment.email] Products slice is empty", map[string]any{
			"intent_id": intent.ID,
		})
		return ""
	}

	firstProduct, ok := productsSlice[0].(map[string]interface{})
	if !ok {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] First product is not a map", map[string]any{
			"intent_id":           intent.ID,
			"first_product_type":  fmt.Sprintf("%T", productsSlice[0]),
			"first_product_value": productsSlice[0],
		})
		return ""
	}

	productID, ok := firstProduct["id"].(string)
	if !ok {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] Product ID is not a string", map[string]any{
			"intent_id":       intent.ID,
			"product_id_type": fmt.Sprintf("%T", firstProduct["id"]),
			"product_id":      firstProduct["id"],
		})
		return ""
	}

	if productID == "" {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] Product ID is empty", map[string]any{
			"intent_id": intent.ID,
		})
		return ""
	}

	paymentItem, kgErr := payment_item.GetPaymentItemByID(ctx, productID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] Failed to get payment item", map[string]any{
			"error":      kgErr.Error.Error(),
			"intent_id":  intent.ID,
			"product_id": productID,
		})
		return ""
	}

	if paymentItem == nil {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] Payment item is nil", map[string]any{
			"intent_id":  intent.ID,
			"product_id": productID,
		})
		return ""
	}

	if paymentItem.SuccessMessage == nil {
		kglog.DebugWithDataCtx(ctx, "[payment.email] Payment item has no success message", map[string]any{
			"intent_id":  intent.ID,
			"product_id": productID,
		})
		return ""
	}

	kglog.InfoWithDataCtx(ctx, "[payment.email] Successfully extracted success message", map[string]any{
		"intent_id":       intent.ID,
		"product_id":      productID,
		"success_message": *paymentItem.SuccessMessage,
	})

	return *paymentItem.SuccessMessage
}

// extractFirstProductName attempts to get the name of the first product from OrderData.
func extractFirstProductName(orderData map[string]any) string {
	if productsData, ok := orderData["products"]; ok {
		if productsSlice, castOk := productsData.([]any); castOk && len(productsSlice) > 0 {
			if firstProductMap, mapOk := productsSlice[0].(map[string]any); mapOk {
				if productName, nameOk := firstProductMap["name"].(string); nameOk && productName != "" {
					return productName
				}
			}
		}
	}
	return ""
}

// OrderDataItem represents a key-value pair from order data for email display
type OrderDataItem struct {
	Key   string
	Value string
}

// extractOrderDataForEmail extracts and formats order data for email display
func extractOrderDataForEmail(orderData map[string]any) []OrderDataItem {
	if len(orderData) == 0 {
		return nil
	}

	var items []OrderDataItem

	// Define the order of keys we want to display (most important first)
	preferredOrder := []string{
		"email",
	}

	// Add preferred keys first
	addedKeys := make(map[string]bool)
	for _, key := range preferredOrder {
		if value, exists := orderData[key]; exists && value != nil {
			if strValue := formatOrderDataValue(value); strValue != "" {
				items = append(items, OrderDataItem{
					Key:   key,
					Value: strValue,
				})
				addedKeys[key] = true
			}
		}
	}

	// Add remaining keys in alphabetical order for consistency
	var remainingKeys []string
	for key := range orderData {
		if !addedKeys[key] {
			remainingKeys = append(remainingKeys, key)
		}
	}

	// Sort remaining keys alphabetically
	for i := 0; i < len(remainingKeys); i++ {
		for j := i + 1; j < len(remainingKeys); j++ {
			if remainingKeys[i] > remainingKeys[j] {
				remainingKeys[i], remainingKeys[j] = remainingKeys[j], remainingKeys[i]
			}
		}
	}

	// Add remaining keys
	for _, key := range remainingKeys {
		if value := orderData[key]; value != nil {
			if strValue := formatOrderDataValue(value); strValue != "" {
				items = append(items, OrderDataItem{
					Key:   key,
					Value: strValue,
				})
			}
		}
	}

	return items
}

// formatOrderDataValue formats a value for display in the email
func formatOrderDataValue(value any) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		trimmed := strings.TrimSpace(v)
		if len(trimmed) > 200 {
			return trimmed[:197] + "..."
		}
		return trimmed
	case int, int64, int32:
		return fmt.Sprintf("%d", v)
	case float64, float32:
		return fmt.Sprintf("%.2f", v)
	case bool:
		if v {
			return "Yes"
		}
		return "No"
	case []any:
		// Handle arrays
		return formatArrayValue(v)
	case map[string]any:
		// Handle objects
		return formatObjectValue(v)
	default:
		// For other complex types, convert to string but truncate if too long
		str := fmt.Sprintf("%v", v)
		if len(str) > 200 {
			return str[:197] + "..."
		}
		return str
	}
}

// formatArrayValue formats an array for display
func formatArrayValue(arr []any) string {
	if len(arr) == 0 {
		return ""
	}

	var parts []string
	for _, item := range arr {
		if itemStr := formatOrderDataValue(item); itemStr != "" {
			parts = append(parts, itemStr)
		}
	}

	if len(parts) == 0 {
		return ""
	}

	// Join with commas, but limit total length
	result := strings.Join(parts, ", ")
	if len(result) > 200 {
		return result[:197] + "..."
	}
	return result
}

// formatObjectValue formats an object for display
func formatObjectValue(obj map[string]any) string {
	if len(obj) == 0 {
		return ""
	}

	var parts []string

	// Try to find meaningful fields first
	meaningfulKeys := []string{"name", "title", "id", "key", "value", "type", "price"}
	for _, key := range meaningfulKeys {
		if value, exists := obj[key]; exists && value != nil {
			if valueStr := formatOrderDataValue(value); valueStr != "" {
				parts = append(parts, fmt.Sprintf("%s: %s", key, valueStr))
			}
		}
	}

	// If we didn't find meaningful fields, add other fields
	if len(parts) == 0 {
		for key, value := range obj {
			if value != nil {
				if valueStr := formatOrderDataValue(value); valueStr != "" {
					parts = append(parts, fmt.Sprintf("%s: %s", key, valueStr))
					// Limit to prevent too much data
					if len(parts) >= 3 {
						break
					}
				}
			}
		}
	}

	if len(parts) == 0 {
		return ""
	}

	// Join with semicolons and limit length
	result := strings.Join(parts, "; ")
	if len(result) > 200 {
		return result[:197] + "..."
	}
	return result
}

// determineEmailSubjectAndTitle generates the email subject and body title
// based on order status, product name, intent status, and application name.
func determineEmailSubjectAndTitle(isOrder bool, firstProductName string, status domain.PaymentIntentStatus, appName string, isOwner bool) (subject, bodyTitle string) {
	statusStr := string(status)

	// For organization owners: notification about a sale/payment
	if isOwner {
		if isOrder {
			if firstProductName != "" {
				bodyTitle = fmt.Sprintf("Product Order Notification: '%s' - %s", firstProductName, statusStr)
				subject = fmt.Sprintf("%s - New Order for '%s': %s", appName, firstProductName, statusStr)
			} else {
				bodyTitle = fmt.Sprintf("Order Notification: %s", statusStr)
				subject = fmt.Sprintf("%s - New Order Status: %s", appName, statusStr)
			}
		} else {
			bodyTitle = "Payment Notification"
			subject = fmt.Sprintf("%s - Payment Notification: %s", appName, statusStr)
		}
	} else {
		// For customers: update about their own order/payment
		if isOrder {
			if firstProductName != "" {
				bodyTitle = fmt.Sprintf("Status of Your Order for '%s': %s", firstProductName, statusStr)
				subject = fmt.Sprintf("%s - Order: '%s' Status: %s", appName, firstProductName, statusStr)
			} else {
				bodyTitle = fmt.Sprintf("Your Order Status: %s", statusStr)
				subject = fmt.Sprintf("%s - Order Status: %s", appName, statusStr)
			}
		} else {
			bodyTitle = "Your Payment Status Update"
			subject = fmt.Sprintf("%s - Payment Status: %s", appName, statusStr)
		}
	}
	return subject, bodyTitle
}

// determineIfFirstSale checks if this is the first successful sale for a specific product
func determineIfFirstSale(ctx context.Context, intent *domain.PaymentIntent) bool {
	// Only check for successful payments
	if intent.Status != domain.PaymentIntentStatusSuccess {
		return false
	}

	// Only check for orders (not plain payments)
	if len(intent.OrderData) == 0 {
		return false
	}

	// Extract product identifier from OrderData
	productID := extractProductIdentifier(intent.OrderData)
	if productID == "" {
		kglog.DebugWithDataCtx(ctx, "[payment.email] No product identifier found for first sale check", map[string]any{
			"intent_id": intent.ID,
		})
		return false
	}

	// Check if there are any other successful payments for the same organization with the same product
	isFirst, err := checkIfFirstSaleForProduct(ctx, intent.OrgID, intent.ClientID, productID, intent.ID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] Failed to check if first sale", map[string]any{
			"error":      err.Error(),
			"intent_id":  intent.ID,
			"org_id":     intent.OrgID,
			"client_id":  intent.ClientID,
			"product_id": productID,
		})
		return false
	}

	kglog.DebugWithDataCtx(ctx, "[payment.email] First sale check result", map[string]any{
		"intent_id":  intent.ID,
		"product_id": productID,
		"is_first":   isFirst,
	})

	return isFirst
}

// extractProductIdentifier extracts a unique identifier for the product from OrderData
// Priority: id > sku > name
func extractProductIdentifier(orderData map[string]any) string {
	if productsData, ok := orderData["products"]; ok {
		if productsSlice, castOk := productsData.([]any); castOk && len(productsSlice) > 0 {
			if firstProductMap, mapOk := productsSlice[0].(map[string]any); mapOk {
				// Try to find a unique identifier in order of preference
				if productID, ok := firstProductMap["id"].(string); ok && productID != "" {
					return productID
				}
				if productSku, ok := firstProductMap["sku"].(string); ok && productSku != "" {
					return productSku
				}
				if productName, ok := firstProductMap["name"].(string); ok && productName != "" {
					return productName
				}
			}
		}
	}
	return ""
}

// checkIfFirstSaleForProduct checks if this is the first successful sale for a specific product
// in the organization/client context
func checkIfFirstSaleForProduct(ctx context.Context, orgID int, clientID, productID, currentIntentID string) (bool, error) {
	if r == nil {
		return false, fmt.Errorf("payment intent repo is not initialized")
	}

	// Get all successful payment intents for this organization/client
	params := domain.GetPaymentIntentsParams{
		OrgID:    orgID,
		ClientID: &clientID,
		Status:   []domain.PaymentIntentStatus{domain.PaymentIntentStatusSuccess},
		Page:     1,
		PageSize: 1000, // Large enough to get all successful intents
	}

	intents, _, err := r.GetPaymentIntents(ctx, params)
	if err != nil {
		return false, fmt.Errorf("failed to query payment intents: %w", err)
	}

	// Check if any other intent has the same product and was created earlier
	for _, intentRecord := range intents {
		// Skip the current intent
		if intentRecord.ID == currentIntentID {
			continue
		}

		// Extract product identifier from this intent's OrderData
		otherProductID := extractProductIdentifier(intentRecord.OrderData)
		if otherProductID == productID {
			// Found another successful sale for the same product
			kglog.DebugWithDataCtx(ctx, "[payment.email] Found existing sale for product", map[string]any{
				"current_intent_id":  currentIntentID,
				"existing_intent_id": intentRecord.ID,
				"product_id":         productID,
			})
			return false, nil
		}
	}

	// No other successful sales found for this product
	return true, nil
}

func prepareEmailParams(ctx context.Context, intent *domain.PaymentIntent, oauthApp *domain.OAuthApplication, appName string, isGenericAppName bool, recipientName string, isOwner bool, successMessage string) (PreparedEmailData, error) {
	isOrder := len(intent.OrderData) > 0
	firstProductName := ""
	if isOrder {
		firstProductName = extractFirstProductName(intent.OrderData)
	}

	emailSubject, bodyTitle := determineEmailSubjectAndTitle(isOrder, firstProductName, intent.Status, appName, isOwner)

	var fiatAmount, fiatCurrency string
	if intent.FiatAmount != nil {
		fiatAmount = intent.FiatAmount.String()
	}
	if intent.FiatCurrency != nil {
		fiatCurrency = *intent.FiatCurrency
	}

	// Extract order data for email display
	var orderDataItems []OrderDataItem
	if isOrder {
		orderDataItems = extractOrderDataForEmail(intent.OrderData)
	}

	isFirstSale := determineIfFirstSale(ctx, intent)

	params := map[string]any{
		"RecipientName":    recipientName,
		"PaymentIntentID":  intent.ID,
		"Status":           string(intent.Status),
		"StatusClass":      strings.ToLower(string(intent.Status)),
		"BodyTitle":        bodyTitle,
		"IsOrder":          isOrder,
		"IsOwner":          isOwner,
		"FirstProductName": firstProductName,
		"IsGenericAppName": isGenericAppName,
		"FiatAmount":       fiatAmount,
		"FiatCurrency":     fiatCurrency,
		"CryptoAmount":     intent.CryptoAmount.String(),
		"Symbol":           intent.Symbol,
		"AppName":          appName,
		"MainLogo":         "", // Default to empty
		"SupportAddress":   "", // Default to empty
		"CurrentYear":      time.Now().Year(),
		"OrderDataItems":   orderDataItems,
		"HasOrderData":     len(orderDataItems) > 0,
		"SuccessMessage":   successMessage,
		"IsFirstSale":      isFirstSale,
	}

	if oauthApp != nil {
		if oauthApp.MainLogo != "" {
			params["MainLogo"] = oauthApp.MainLogo
		}
		if oauthApp.SupportAddress != "" {
			params["SupportAddress"] = oauthApp.SupportAddress
		}
	}

	// Try to get success_message from payment item when status is success
	if intent.Status == domain.PaymentIntentStatusSuccess {
		params["SuccessMessage"] = successMessage
	}

	if intent.ReceivedCryptoAmount != nil {
		params["ReceivedAmount"] = intent.ReceivedCryptoAmount.String()
	}
	if intent.PaymentTxHash != nil {
		params["PaymentTxHash"] = *intent.PaymentTxHash
		// Generate explorer URL for the transaction hash
		params["PaymentTxURL"] = intent.PaymentChain.TransactionURL(*intent.PaymentTxHash)
	}
	if intent.PaymentTxTimestamp != nil {
		params["PaymentTxTimestamp"] = intent.PaymentTxTimestamp.Format(time.RFC1123)
	}

	// Order data is now formatted as a structured table for better readability
	// instead of raw JSON, making the emails cleaner and more professional

	return PreparedEmailData{Subject: emailSubject, Parameters: params}, nil
}

func sendEmailToRecipient(ctx context.Context, toEmail, subject string, params map[string]any) {
	kglog.InfoWithDataCtx(ctx, "[payment.email] Attempting to send email", map[string]any{
		"to":        toEmail,
		"subject":   subject,
		"intent_id": params["PaymentIntentID"],
		"status":    params["Status"],
	})

	sendgridClient := sendgrid.NewClient()
	resp, err := sendgridClient.SendEmailWithSubject(ctx, toEmail, subject, sendgrid.EmailTypePaymentIntentStatusChanged, params)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[payment.email] SendEmail failed", map[string]any{
			"error":     err.Error(),
			"to":        toEmail,
			"intent_id": params["PaymentIntentID"],
			"status":    params["Status"],
		})
	} else if resp != nil && resp.StatusCode >= 300 {
		respBody := string(resp.Body)
		kglog.ErrorWithDataCtx(ctx, "[payment.email] SendEmail returned non-2xx status", map[string]any{
			"to":          toEmail,
			"intent_id":   params["PaymentIntentID"],
			"status":      params["Status"],
			"status_code": resp.StatusCode,
			"resp_body":   respBody,
		})
	} else {
		kglog.InfoWithDataCtx(ctx, "[payment.email] Payment status email sent successfully", map[string]any{
			"to":        toEmail,
			"intent_id": params["PaymentIntentID"],
			"status":    params["Status"],
		})
	}
}
