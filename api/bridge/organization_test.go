package bridge

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	bridgeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/bridge-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/bridge"
	bridgefee "github.com/kryptogo/kg-wallet-backend/service/bridge-fee"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupBridgeOrganizationAPITest sets up test environment for bridge organization API tests
func setupBridgeOrganizationAPITest(t *testing.T) (*gin.Engine, string) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)
	
	// Reset database
	rdb.Reset()
	
	// Initialize dependencies
	uRepo := repo.Unified()
	oauth.Init(uRepo)
	application.Init(uRepo)
	bridgeapi.InitDefault()
	apiClient := bridgeapi.Get()
	bridge.Init(uRepo, apiClient)
	bridgefee.Init(uRepo)
	
	// Create test data
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "Test Organization"},
		{ID: 2, Name: "Another Organization"},
	}).Error)
	
	assert.Nil(t, rdb.Get().Create([]model.OAuthClientConfig{
		{
			ID:           "test_client_id",
			Domain:       "http://test.com",
			IsPrivileged: true,
			Name:         "Test App",
			Secret:       "test_secret",
			IsCustomAuth: false,
		},
	}).Error)
	
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationClient{
		{
			OrganizationID:  1,
			ClientID:        "test_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
	}).Error)
	
	// Create test user
	users, uid, _, _ := dbtest.User()
	assert.Nil(t, rdb.GormRepo().BatchSetUsers(context.Background(), users))
	
	// Create organization user (using StudioUser)
	assert.Nil(t, rdb.Get().Create([]model.StudioUser{
		{
			OrganizationID: 1,
			UID:            uid,
			Name:           "Test User",
		},
	}).Error)
	
	// Setup router
	server := gin.Default()
	
	// Add authentication middleware
	authGroup := server.Group("/")
	authGroup.Use(auth.AuthorizeByKgTokenOrKgStudioToken())
	authGroup.Use(auth.MockOrgID(1)) // Mock organization ID for testing
	authGroup.POST("/bridge/organization", CreateBridgeOrganization)
	
	// Add GET endpoints with organization validation
	orgAuth := authGroup.Group("/organization/:orgID")
	orgAuth.Use(middleware.OrganizationUserValidation())
	orgAuth.GET("/bridge/external_accounts", GetBridgeExternalAccounts)
	orgAuth.GET("/bridge/external_account/:external_account_id", GetBridgeExternalAccount)
	
	return server, uid
}

func TestCreateBridgeOrganization_Success(t *testing.T) {
	server, uid := setupBridgeOrganizationAPITest(t)
	
	// Create request payload
	requestData := map[string]interface{}{
		"email":     fmt.Sprintf("<EMAIL>", time.Now().Unix()),
		"full_name": "Test User",
	}
	
	// Marshal request data
	jsonData, err := json.Marshal(requestData)
	require.NoError(t, err)
	
	// Create HTTP request
	req, err := http.NewRequest("POST", "/bridge/organization", bytes.NewBuffer(jsonData))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")
	
	// Add authentication headers
	req.Header.Set("KG-TOKEN", generateTestToken(t, uid))
	req.Header.Set("KG-DEV-UID", uid)
	req.Header.Set("Client-ID", "test_client_id")
	
	// Execute request
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	
	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)
	
	// Parse response
	var apiResponse response.OKResp
	err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
	require.NoError(t, err)
	
	// Check response structure
	assert.Equal(t, code.OK, apiResponse.Code)
	assert.NotNil(t, apiResponse.Data)
	
	// Parse response data
	responseData, ok := apiResponse.Data.(map[string]interface{})
	require.True(t, ok, "Response data should be a map")
	
	// Verify response fields
	assert.NotEmpty(t, responseData["customer_id"], "CustomerID should not be empty")
	assert.Equal(t, requestData["email"], responseData["email"], "Email should match")
	assert.Equal(t, requestData["full_name"], responseData["full_name"], "FullName should match")
	assert.Equal(t, "business", responseData["type"], "Type should be business")
	assert.NotEmpty(t, responseData["kyc_link"], "KYCLink should not be empty")
	assert.NotEmpty(t, responseData["tos_link"], "TOSLink should not be empty")
	
	t.Logf("✅ Bridge organization created successfully")
	t.Logf("CustomerID: %v", responseData["customer_id"])
	t.Logf("KYC Link: %v", responseData["kyc_link"])
	t.Logf("TOS Link: %v", responseData["tos_link"])
}

func TestCreateBridgeOrganization_ValidationErrors(t *testing.T) {
	server, uid := setupBridgeOrganizationAPITest(t)
	
	testCases := []struct {
		name           string
		requestData    map[string]interface{}
		expectedStatus int
		expectedCode   int
	}{
		{
			name:           "missing_email",
			requestData:    map[string]interface{}{"full_name": "Test User"},
			expectedStatus: http.StatusBadRequest,
			expectedCode:   code.ParamIncorrect,
		},
		{
			name:           "missing_full_name",
			requestData:    map[string]interface{}{"email": "<EMAIL>"},
			expectedStatus: http.StatusBadRequest,
			expectedCode:   code.ParamIncorrect,
		},
		{
			name:           "empty_request",
			requestData:    map[string]interface{}{},
			expectedStatus: http.StatusBadRequest,
			expectedCode:   code.ParamIncorrect,
		},
		{
			name:           "invalid_email_format",
			requestData:    map[string]interface{}{"email": "invalid-email", "full_name": "Test User"},
			expectedStatus: http.StatusBadRequest,
			expectedCode:   code.ParamIncorrect,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Marshal request data
			jsonData, err := json.Marshal(tc.requestData)
			require.NoError(t, err)
			
			// Create HTTP request
			req, err := http.NewRequest("POST", "/bridge/organization", bytes.NewBuffer(jsonData))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("KG-TOKEN", generateTestToken(t, uid))
			req.Header.Set("KG-DEV-UID", uid)
			req.Header.Set("Client-ID", "test_client_id")
			
			// Execute request
			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)
			
			// Verify response
			assert.Equal(t, tc.expectedStatus, w.Code)
			
			// Parse error response
			var apiResponse response.ErrorResp
			err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
			require.NoError(t, err)
			
			assert.Equal(t, tc.expectedCode, apiResponse.Code)
			assert.NotEmpty(t, apiResponse.Message)
			
			t.Logf("Expected validation error: %s", apiResponse.Message)
		})
	}
}

func TestCreateBridgeOrganization_AuthenticationErrors(t *testing.T) {
	server, _ := setupBridgeOrganizationAPITest(t)
	
	requestData := map[string]interface{}{
		"email":     "<EMAIL>",
		"full_name": "Test User",
	}
	
	testCases := []struct {
		name         string
		setupRequest func(*http.Request)
		expectedCode int
	}{
		{
			name: "missing_auth_header",
			setupRequest: func(req *http.Request) {
				req.Header.Set("Client-ID", "test_client_id")
			},
			expectedCode: http.StatusUnauthorized,
		},
		{
			name: "missing_client_id",
			setupRequest: func(req *http.Request) {
				req.Header.Set("KG-TOKEN", "invalid-token")
				req.Header.Set("KG-DEV-UID", "test-uid")
			},
			expectedCode: http.StatusUnauthorized,
		},
		{
			name: "invalid_token",
			setupRequest: func(req *http.Request) {
				req.Header.Set("KG-TOKEN", "invalid-token")
				req.Header.Set("KG-DEV-UID", "test-uid")
				req.Header.Set("Client-ID", "test_client_id")
			},
			expectedCode: http.StatusUnauthorized,
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Marshal request data
			jsonData, err := json.Marshal(requestData)
			require.NoError(t, err)
			
			// Create HTTP request
			req, err := http.NewRequest("POST", "/bridge/organization", bytes.NewBuffer(jsonData))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")
			
			// Setup request headers based on test case
			tc.setupRequest(req)
			
			// Execute request
			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)
			
			// Verify response
			assert.Equal(t, tc.expectedCode, w.Code)
			
			t.Logf("Expected authentication error with status: %d", w.Code)
		})
	}
}

func TestCreateBridgeOrganization_RealAPI(t *testing.T) {
	// This test makes a real call to the Bridge API through the HTTP endpoint
	// Skip if we don't want to make external calls
	if testing.Short() {
		t.Skip("Skipping external API test in short mode")
	}
	
	// Verify that API credentials are configured
	apiKey := config.GetString("BRIDGE_API_KEY")
	baseURL := config.GetString("BRIDGE_API_URL")
	
	if apiKey == "" || baseURL == "" {
		t.Skip("Bridge API credentials not configured")
	}
	
	server, uid := setupBridgeOrganizationAPITest(t)
	
	// Use a unique email to avoid conflicts
	uniqueEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	
	requestData := map[string]interface{}{
		"email":     uniqueEmail,
		"full_name": "Real API Test User",
	}
	
	t.Run("real_api_http_call", func(t *testing.T) {
		// Marshal request data
		jsonData, err := json.Marshal(requestData)
		require.NoError(t, err)
		
		// Create HTTP request
		req, err := http.NewRequest("POST", "/bridge/organization", bytes.NewBuffer(jsonData))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("KG-TOKEN", generateTestToken(t, uid))
		req.Header.Set("KG-DEV-UID", uid)
		req.Header.Set("Client-ID", "test_client_id")
		
		// Execute request
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		
		// Log response for debugging
		t.Logf("Response Status: %d", w.Code)
		t.Logf("Response Body: %s", w.Body.String())
		
		// Parse response based on status
		if w.Code == http.StatusOK {
			// Success case
			var apiResponse response.OKResp
			err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
			require.NoError(t, err)
			
			assert.Equal(t, code.OK, apiResponse.Code)
			
			responseData, ok := apiResponse.Data.(map[string]interface{})
			require.True(t, ok, "Response data should be a map")
			
			// Verify response fields
			assert.NotEmpty(t, responseData["customer_id"], "CustomerID should not be empty")
			assert.Equal(t, uniqueEmail, responseData["email"], "Email should match")
			assert.Equal(t, "Real API Test User", responseData["full_name"], "FullName should match")
			assert.Equal(t, "business", responseData["type"], "Type should be business")
			assert.NotEmpty(t, responseData["kyc_link"], "KYC link should be provided")
			assert.NotEmpty(t, responseData["tos_link"], "TOS link should be provided")
			
			t.Logf("✅ Real API HTTP call successful!")
			t.Logf("CustomerID: %v", responseData["customer_id"])
			t.Logf("KYC Link: %v", responseData["kyc_link"])
			t.Logf("TOS Link: %v", responseData["tos_link"])
			
		} else {
			// Error case - this might be expected in test environments
			var apiResponse response.ErrorResp
			err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
			require.NoError(t, err)
			
			t.Logf("API call failed (this might be expected in test environment)")
			t.Logf("Error Code: %d", apiResponse.Code)
			t.Logf("Error Message: %s", apiResponse.Message)
			
			// We still verify that the error response is properly structured
			assert.NotEqual(t, code.OK, apiResponse.Code, "Should return proper error code")
			assert.NotEmpty(t, apiResponse.Message, "Should return error message")
		}
	})
}

// TestGetBridgeExternalAccounts tests the GET /bridge/external_accounts endpoint
func TestGetBridgeExternalAccounts(t *testing.T) {
	server, uid := setupBridgeOrganizationAPITest(t)
	
	// Create test external accounts directly in database
	testOrgID := 1
	customerID := "test-customer-for-api-get"
	
	// Create bridge organization first
	bridgeOrgData := &domain.CreateBridgeOrganizationData{
		OrganizationID:   testOrgID,
		CustomerID:       customerID,
		FullName:         "Test User for API GET",
		Email:            "<EMAIL>",
		Type:             "business",
		KYCStatus:        "approved",
		TOSStatus:        "approved",
		RejectionReasons: []string{},
	}
	
	repo := rdb.GormRepo()
	err := repo.CreateBridgeOrganization(context.Background(), bridgeOrgData)
	require.NoError(t, err, "Failed to create bridge organization")
	
	// Create test external accounts
	testAccounts := []*domain.CreateBridgeExternalAccountData{
		{
			BridgeExternalAccountID: "api-test-account-1",
			CustomerID:              customerID,
			OrganizationID:          testOrgID,
			BankName:                "API Test Bank 1",
			AccountOwnerName:        "API Test Owner 1",
			Active:                  true,
			Currency:                "usd",
			AccountOwnerType:        "business",
			AccountType:             "iban",
		},
		{
			BridgeExternalAccountID: "api-test-account-2",
			CustomerID:              customerID,
			OrganizationID:          testOrgID,
			BankName:                "API Test Bank 2",
			AccountOwnerName:        "API Test Owner 2",
			Active:                  true,
			Currency:                "usd",
			AccountOwnerType:        "individual",
			AccountType:             "us",
		},
	}
	
	for _, accountData := range testAccounts {
		err := repo.CreateBridgeExternalAccount(context.Background(), accountData)
		require.NoError(t, err, "Failed to create test external account")
	}
	
	t.Run("get_external_accounts_success", func(t *testing.T) {
		// Create HTTP request with organization ID in path
		req, err := http.NewRequest("GET", "/organization/1/bridge/external_accounts", nil)
		require.NoError(t, err)
		
		// Add authentication headers for development token
		req.Header.Set("KG-STUDIO-TOKEN-V2", generateTestToken(t, uid))
		req.Header.Set("KG-DEV-UID", uid)
		
		// Execute request
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		
		// Verify response
		assert.Equal(t, http.StatusOK, w.Code)
		
		// Parse response
		var apiResponse response.OKResp
		err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
		require.NoError(t, err)
		
		// Check response structure
		assert.Equal(t, code.OK, apiResponse.Code)
		require.NotNil(t, apiResponse.Data)
		
		// Parse response data as array
		accounts, ok := apiResponse.Data.([]interface{})
		require.True(t, ok, "Response data should be an array")
		assert.Len(t, accounts, 2, "Should return 2 accounts")
		
		// Verify account details
		for _, accountInterface := range accounts {
			account, ok := accountInterface.(map[string]interface{})
			require.True(t, ok, "Each account should be a map")
			
			assert.NotEmpty(t, account["bridge_external_account_id"], "Account ID should not be empty")
			assert.Equal(t, customerID, account["customer_id"], "Customer ID should match")
			assert.Equal(t, float64(testOrgID), account["organization_id"], "Organization ID should match")
			assert.NotEmpty(t, account["bank_name"], "Bank name should not be empty")
			assert.NotEmpty(t, account["account_owner_name"], "Account owner name should not be empty")
		}
		
		t.Logf("✅ Successfully retrieved %d external accounts via API", len(accounts))
	})
	
	t.Run("get_external_account_by_id_success", func(t *testing.T) {
		// Create HTTP request for specific account with organization ID in path
		req, err := http.NewRequest("GET", "/organization/1/bridge/external_account/api-test-account-1", nil)
		require.NoError(t, err)
		
		// Add authentication headers for development token
		req.Header.Set("KG-STUDIO-TOKEN-V2", generateTestToken(t, uid))
		req.Header.Set("KG-DEV-UID", uid)
		
		// Execute request
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		
		// Verify response
		assert.Equal(t, http.StatusOK, w.Code)
		
		// Parse response
		var apiResponse response.OKResp
		err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
		require.NoError(t, err)
		
		// Check response structure
		assert.Equal(t, code.OK, apiResponse.Code)
		require.NotNil(t, apiResponse.Data)
		
		// Parse response data
		account, ok := apiResponse.Data.(map[string]interface{})
		require.True(t, ok, "Response data should be a map")
		
		// Verify specific account details
		assert.Equal(t, "api-test-account-1", account["bridge_external_account_id"], "Account ID should match")
		assert.Equal(t, customerID, account["customer_id"], "Customer ID should match")
		assert.Equal(t, float64(testOrgID), account["organization_id"], "Organization ID should match")
		assert.Equal(t, "API Test Bank 1", account["bank_name"], "Bank name should match")
		assert.Equal(t, "API Test Owner 1", account["account_owner_name"], "Account owner name should match")
		
		t.Logf("✅ Successfully retrieved specific external account via API: %s", account["bridge_external_account_id"])
	})
	
	t.Run("get_external_account_by_id_not_found", func(t *testing.T) {
		// Create HTTP request for non-existent account with organization ID in path
		req, err := http.NewRequest("GET", "/organization/1/bridge/external_account/non-existent-account", nil)
		require.NoError(t, err)
		
		// Add authentication headers for development token
		req.Header.Set("KG-STUDIO-TOKEN-V2", generateTestToken(t, uid))
		req.Header.Set("KG-DEV-UID", uid)
		
		// Execute request
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		
		// Verify response
		assert.Equal(t, http.StatusNotFound, w.Code)
		
		// Parse error response
		var apiResponse response.ErrorResp
		err = json.Unmarshal(w.Body.Bytes(), &apiResponse)
		require.NoError(t, err)
		
		assert.NotEqual(t, code.OK, apiResponse.Code)
		assert.NotEmpty(t, apiResponse.Message)
		
		t.Logf("✅ Correctly returned 404 for non-existent account: %s", apiResponse.Message)
	})
}

// Helper function to generate test tokens (simplified for testing)
func generateTestToken(t *testing.T, uid string) string {
	// Use development token for testing
	return "KG-DEV:123456"
}

// Benchmark test for API performance
func BenchmarkCreateBridgeOrganizationAPI(b *testing.B) {
	// Note: This benchmark will make real API calls
	// Consider mocking external dependencies for pure performance testing
	
	gin.SetMode(gin.TestMode)
	server, uid := setupBridgeOrganizationAPIBenchmark(b)
	
	// Pre-create request data
	requestData := map[string]interface{}{
		"email":     "<EMAIL>",
		"full_name": "Benchmark User",
	}
	
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		// Use unique email for each iteration
		requestData["email"] = fmt.Sprintf("<EMAIL>", time.Now().Unix(), i)
		
		jsonData, _ := json.Marshal(requestData)
		
		req, _ := http.NewRequest("POST", "/bridge/organization", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("KG-TOKEN", generateTestToken(nil, uid))
		req.Header.Set("KG-DEV-UID", uid)
		req.Header.Set("Client-ID", "test_client_id")
		
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
	}
}

// Helper function for benchmark setup
func setupBridgeOrganizationAPIBenchmark(b *testing.B) (*gin.Engine, string) {
	gin.SetMode(gin.TestMode)
	rdb.Reset()
	
	// Initialize dependencies
	uRepo := repo.Unified()
	oauth.Init(uRepo)
	application.Init(uRepo)
	bridgeapi.InitDefault()
	apiClient := bridgeapi.Get()
	bridge.Init(uRepo, apiClient)
	bridgefee.Init(uRepo)
	
	// Create test data
	if err := rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "Benchmark Organization"},
	}).Error; err != nil {
		b.Fatal(err)
	}
	
	if err := rdb.Get().Create([]model.OAuthClientConfig{
		{
			ID:           "test_client_id",
			Domain:       "http://test.com",
			IsPrivileged: true,
			Name:         "Test App",
			Secret:       "test_secret",
			IsCustomAuth: false,
		},
	}).Error; err != nil {
		b.Fatal(err)
	}
	
	if err := rdb.Get().Create([]model.StudioOrganizationClient{
		{
			OrganizationID:  1,
			ClientID:        "test_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
	}).Error; err != nil {
		b.Fatal(err)
	}
	
	// Create test user
	users, uid, _, _ := dbtest.User()
	if err := rdb.GormRepo().BatchSetUsers(context.Background(), users); err != nil {
		b.Fatal(err)
	}
	
	// Setup router
	server := gin.Default()
	authGroup := server.Group("/")
	authGroup.Use(auth.AuthorizeByKgTokenOrKgStudioToken())
	authGroup.POST("/bridge/organization", CreateBridgeOrganization)
	
	return server, uid
} 