package bridge

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/bridge"
)

type createBridgeOrganizationRequest struct {
	Email    string `json:"email" binding:"required"`
	FullName string `json:"full_name" binding:"required"`
}

// CreateBridgeOrganization creates a bridge organization
func CreateBridgeOrganization(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.CreateBridgeOrganization")
	defer span.End()

	var req createBridgeOrganizationRequest
	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get organization ID from context (set by middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "organization ID not found in context")
		return
	}

	kglog.InfoWithDataCtx(ctx, "Creating bridge organization API request", map[string]interface{}{
		"organization_id": orgID,
		"email":          req.Email,
		"full_name":      req.FullName,
	})

	// Create domain request
	serviceReq := &domain.CreateBridgeOrganizationRequest{
		OrganizationID: orgID,
		Email:          req.Email,
		FullName:       req.FullName,
	}



	result, kgErr := bridge.CreateBridgeOrganization(ctx, serviceReq)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create bridge organization", map[string]interface{}{
			"organization_id": orgID,
			"error":          kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created bridge organization", map[string]interface{}{
		"organization_id": orgID,
		"customer_id":     result.CustomerID,
	})

	response.OK(c, result)
}

// CreateBridgeExternalAccount creates a bridge external account
func CreateBridgeExternalAccount(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.CreateBridgeExternalAccount")
	defer span.End()

	// Get organization ID from context (set by middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "organization ID not found in context")
		return
	}

	var req domain.CreateBridgeExternalAccountRequest
	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Set organization ID from context
	req.OrganizationID = orgID

	// Validate account type specific fields
	if err := req.ValidateAccountTypeSpecificFields(); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Account type validation failed", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"account_type":    req.AccountType,
			"error":          err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	kglog.InfoWithDataCtx(ctx, "Creating bridge external account via API", map[string]interface{}{
		"organization_id":    req.OrganizationID,
		"currency":           req.Currency,
		"bank_name":          req.BankName,
		"account_owner_name": req.AccountOwnerName,
		"account_owner_type": req.AccountOwnerType,
		"account_type":       req.AccountType,
	})


	resp, kgErr := bridge.CreateBridgeExternalAccount(ctx, &req)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create bridge external account", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"error":          kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created bridge external account via API", map[string]interface{}{
		"organization_id":     req.OrganizationID,
		"external_account_id": resp.ID,
		"customer_id":         resp.CustomerID,
		"active":              resp.Active,
	})

	response.OK(c, resp)
}

// GetBridgeExternalAccounts retrieves all bridge external accounts for an organization
func GetBridgeExternalAccounts(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.GetBridgeExternalAccounts")
	defer span.End()

	// Get organization ID from context (set by middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "organization ID not found in context")
		return
	}

	kglog.InfoWithDataCtx(ctx, "Getting bridge external accounts via API", map[string]interface{}{
		"organization_id": orgID,
	})



	accounts, kgErr := bridge.GetBridgeExternalAccountsByOrgID(ctx, orgID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge external accounts", map[string]interface{}{
			"organization_id": orgID,
			"error":          kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge external accounts via API", map[string]interface{}{
		"organization_id": orgID,
		"count":          len(accounts),
	})

	response.OK(c, accounts)
}

// GetBridgeExternalAccount retrieves a specific bridge external account by ID
func GetBridgeExternalAccount(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.GetBridgeExternalAccount")
	defer span.End()

	// Get organization ID from context (set by middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "organization ID not found in context")
		return
	}

	// Get external account ID from path parameter
	externalAccountID := c.Param("external_account_id")
	if externalAccountID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "external account ID is required")
		return
	}

	kglog.InfoWithDataCtx(ctx, "Getting bridge external account by ID via API", map[string]interface{}{
		"organization_id":     orgID,
		"external_account_id": externalAccountID,
	})


	account, kgErr := bridge.GetBridgeExternalAccountByID(ctx, externalAccountID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge external account", map[string]interface{}{
			"organization_id":     orgID,
			"external_account_id": externalAccountID,
			"error":              kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	// Verify that the account belongs to the organization
	if account.OrganizationID != orgID {
		kglog.WarningWithDataCtx(ctx, "Bridge external account does not belong to organization", map[string]interface{}{
			"organization_id":           orgID,
			"external_account_id":       externalAccountID,
			"account_organization_id":   account.OrganizationID,
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, "external account not found for this organization")
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge external account via API", map[string]interface{}{
		"organization_id":     orgID,
		"external_account_id": externalAccountID,
		"customer_id":         account.CustomerID,
	})

	response.OK(c, account)
} 