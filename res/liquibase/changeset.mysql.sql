-- liquibase formatted sql

-- changeset auto:main:********-1
CREATE TABLE IF NOT EXISTS airdrop_events (event_id VARCHAR(36) NOT NULL, chain_id VARCHAR(20) NOT NULL, from_address VARCHAR(45) NOT NULL, contract_address VARCHAR(45) NOT NULL, max_supply INT NOT NULL, total_supply INT DEFAULT 0 NOT NULL, start_time timestamp NOT NULL, end_time timestamp NOT NULL, msg_content TEXT NULL, `description` TEXT NULL, image_url JSON NULL, title TEXT NULL, CONSTRAINT PK_AIRDROP_EVENTS PRIMARY KEY (event_id));
-- rollback DROP TABLE airdrop_events;

-- changeset auto:main:********-2
CREATE TABLE IF NOT EXISTS airdrop_logs (id INT AUTO_INCREMENT NOT NULL, event_id VARCHAR(36) NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(42) NOT NULL, from_address VARCHAR(42) NOT NULL, to_address VARCHAR(42) NOT NULL, tx_hash VARCHAR(66) NOT NULL, tx_time timestamp DEFAULT NOW() NOT NULL, nonce TEXT NOT NULL, status VARCHAR(45) NULL, CONSTRAINT PK_AIRDROP_LOGS PRIMARY KEY (id));
-- rollback DROP TABLE airdrop_logs;

-- changeset auto:main:********-3
CREATE TABLE IF NOT EXISTS alchemy_addresses (uid VARCHAR(60) NOT NULL, chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, CONSTRAINT PK_ALCHEMY_ADDRESSES PRIMARY KEY (uid, chain_id, address));
-- rollback DROP TABLE alchemy_addresses;

-- changeset auto:main:********-4
CREATE TABLE IF NOT EXISTS asset_price_histories (chain_id VARCHAR(100) NOT NULL, asset_group VARCHAR(100) NOT NULL, price VARCHAR(100) NOT NULL, created_at timestamp NOT NULL, CONSTRAINT PK_ASSET_PRICE_HISTORIES PRIMARY KEY (chain_id, asset_group, created_at));
-- rollback DROP TABLE asset_price_histories;

-- changeset auto:main:********-5
CREATE TABLE IF NOT EXISTS asset_prices (chain_id VARCHAR(100) NOT NULL, asset_group VARCHAR(100) NOT NULL, price VARCHAR(100) NOT NULL, CONSTRAINT PK_ASSET_PRICES PRIMARY KEY (chain_id, asset_group));
-- rollback DROP TABLE asset_prices;

-- changeset auto:main:********-6
CREATE TABLE IF NOT EXISTS assets (chain_id VARCHAR(100) NOT NULL, asset_group VARCHAR(200) NOT NULL, amount VARCHAR(256) NULL, wallet_address VARCHAR(100) NOT NULL, asset_type ENUM('token', 'defi', 'nft') NOT NULL, metadata JSON NULL, created_at timestamp NOT NULL, last_modified_start timestamp NULL, name VARCHAR(100) NOT NULL, logo_urls JSON NOT NULL, is_verified tinyint(1) NULL, symbol VARCHAR(100) NULL, token_account VARCHAR(100) NULL, token_type VARCHAR(100) NULL, decimals INT NULL, CONSTRAINT PK_ASSETS PRIMARY KEY (chain_id, asset_group, wallet_address, asset_type));
-- rollback DROP TABLE assets;

-- changeset auto:main:********-7
CREATE TABLE IF NOT EXISTS dashboard_prizes (id INT AUTO_INCREMENT NOT NULL, title VARCHAR(64) NULL, detail TEXT NULL, limitation TEXT NULL, merchant_name VARCHAR(64) NULL, merchant_contact TEXT NULL, amount INT NOT NULL, start_time timestamp NOT NULL, end_time timestamp NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(42) NOT NULL, project_id INT NOT NULL, owner_address VARCHAR(42) NOT NULL, image_url VARCHAR(255) NULL, publish_time timestamp NULL, project_name VARCHAR(250) NULL, CONSTRAINT PK_DASHBOARD_PRIZES PRIMARY KEY (id));
-- rollback DROP TABLE dashboard_prizes;

-- changeset auto:main:********-8
CREATE TABLE IF NOT EXISTS dashboard_projects (id INT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(42) NOT NULL, collection_slug VARCHAR(100) NOT NULL, name VARCHAR(250) NULL, `description` VARCHAR(511) NULL, image_url VARCHAR(255) NOT NULL, large_image_url VARCHAR(255) NOT NULL, owner_address VARCHAR(42) NOT NULL, created_at timestamp NULL, CONSTRAINT PK_DASHBOARD_PROJECTS PRIMARY KEY (id));
-- rollback DROP TABLE dashboard_projects;

-- changeset auto:main:********-9
CREATE TABLE IF NOT EXISTS dashboard_redeem_logs (id INT AUTO_INCREMENT NOT NULL, prize_id INT NOT NULL, token_id INT NOT NULL, quantity INT NOT NULL, redeemed_time timestamp NOT NULL, project_id INT NOT NULL, CONSTRAINT PK_DASHBOARD_REDEEM_LOGS PRIMARY KEY (id));
-- rollback DROP TABLE dashboard_redeem_logs;

-- changeset auto:main:********-10
CREATE TABLE IF NOT EXISTS dashboard_users (uid INT AUTO_INCREMENT NOT NULL, owner_address VARCHAR(42) NOT NULL, created_at timestamp NOT NULL, created_ip VARCHAR(39) NOT NULL, created_user_agent TEXT NOT NULL, last_login_at timestamp NOT NULL, last_login_ip VARCHAR(39) NOT NULL, last_login_user_agent TEXT NOT NULL, CONSTRAINT PK_DASHBOARD_USERS PRIMARY KEY (uid), UNIQUE (owner_address));
-- rollback DROP TABLE dashboard_users;

-- changeset auto:main:********-11
CREATE TABLE IF NOT EXISTS data_contract_metadata (id INT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(60) NOT NULL, name VARCHAR(250) NOT NULL, symbol VARCHAR(250) NOT NULL, contract_schema_name VARCHAR(10) NOT NULL, original_synced_at VARCHAR(30) NOT NULL, created_at timestamp NOT NULL, decimals SMALLINT UNSIGNED DEFAULT 0 NOT NULL, icon VARCHAR(250) NOT NULL, coingecko_id VARCHAR(100) NOT NULL, CONSTRAINT PK_DATA_CONTRACT_METADATA PRIMARY KEY (id));
-- rollback DROP TABLE data_contract_metadata;

-- changeset auto:main:********-12
CREATE TABLE IF NOT EXISTS historical_balances (wallet_address VARCHAR(100) NOT NULL, chain_id VARCHAR(100) NOT NULL, created_at timestamp NOT NULL, is_day tinyint(1) NOT NULL, balance VARCHAR(100) NOT NULL, asset_type ENUM('token', 'defi', 'nft') NULL);
-- rollback DROP TABLE historical_balances;

-- changeset auto:main:********-13
CREATE TABLE IF NOT EXISTS nft_asset_tags (id INT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(42) NOT NULL, token_id VARCHAR(100) NOT NULL, uid VARCHAR(42) NOT NULL, tag ENUM('HIDDEN', 'FAVORITE') NOT NULL, CONSTRAINT PK_NFT_ASSET_TAGS PRIMARY KEY (id));
-- rollback DROP TABLE nft_asset_tags;

-- changeset auto:main:********-14
CREATE TABLE IF NOT EXISTS nft_assets (id INT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(42) NOT NULL, token_id VARCHAR(100) NOT NULL, collection_slug VARCHAR(100) NULL, contract_schema_name VARCHAR(10) NULL, image_url VARCHAR(255) NOT NULL, image_preview_url VARCHAR(255) NOT NULL, last_price DECIMAL(10, 6) NOT NULL, last_price_symbol VARCHAR(100) NOT NULL, creator JSON NULL, traits JSON NULL, modified_at timestamp DEFAULT NOW() NOT NULL, listing_time INT DEFAULT 0 NOT NULL, name VARCHAR(250) NOT NULL, collection_name VARCHAR(250) NULL, collection_image_url VARCHAR(250) NULL, update_retry_cnt TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL, update_retry_after timestamp DEFAULT NOW() NOT NULL, update_priority TINYINT(3) DEFAULT 0 NOT NULL, CONSTRAINT PK_NFT_ASSETS PRIMARY KEY (id));
-- rollback DROP TABLE nft_assets;

-- changeset auto:main:********-15
CREATE TABLE IF NOT EXISTS nft_collections (id INT AUTO_INCREMENT NOT NULL, slug VARCHAR(100) NOT NULL, floor_price DECIMAL(18, 6) NOT NULL, floor_price_symbol VARCHAR(100) NOT NULL, modified_at timestamp DEFAULT NOW() NOT NULL, total_volume DECIMAL(18, 6) UNSIGNED DEFAULT 0 NOT NULL, `history` TINYINT(3) DEFAULT 0 NOT NULL, CONSTRAINT PK_NFT_COLLECTIONS PRIMARY KEY (id), UNIQUE (slug));
-- rollback DROP TABLE nft_collections;

-- changeset auto:main:********-16
CREATE TABLE IF NOT EXISTS nft_raw_data (id VARCHAR(250) NOT NULL, data TEXT NOT NULL, modified_at timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_NFT_RAW_DATA PRIMARY KEY (id));
-- rollback DROP TABLE nft_raw_data;

-- changeset auto:main:********-17
CREATE TABLE IF NOT EXISTS nft_sync_owners (id INT AUTO_INCREMENT NOT NULL, owner_address VARCHAR(42) NOT NULL, created_at timestamp NOT NULL, last_modified_start timestamp NULL, last_modified_end timestamp NULL, CONSTRAINT PK_NFT_SYNC_OWNERS PRIMARY KEY (id), UNIQUE (owner_address));
-- rollback DROP TABLE nft_sync_owners;

-- changeset auto:main:********-18
CREATE TABLE IF NOT EXISTS nft_user_amounts (id INT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(42) NOT NULL, token_id VARCHAR(100) NOT NULL, owner_address VARCHAR(42) NOT NULL, amount VARCHAR(100) DEFAULT '0' NOT NULL, modified_at timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_NFT_USER_AMOUNTS PRIMARY KEY (id));
-- rollback DROP TABLE nft_user_amounts;

-- changeset auto:main:********-19
CREATE TABLE IF NOT EXISTS notifications (id INT AUTO_INCREMENT NOT NULL, receiver VARCHAR(32) NOT NULL, content_type ENUM('text', 'html') NOT NULL, message_type ENUM('transaction', 'announcement') NOT NULL, title TEXT NOT NULL, summary TEXT NOT NULL, message TEXT NOT NULL, primary_link VARCHAR(255) NOT NULL, secondary_link VARCHAR(255) NOT NULL, created_at timestamp NOT NULL, primary_text VARCHAR(255) NOT NULL, primary_open_with ENUM('', 'system', 'dapp', 'deep_link') NOT NULL, secondary_text VARCHAR(255) NOT NULL, secondary_open_with ENUM('', 'system', 'dapp', 'deep_link') NOT NULL, CONSTRAINT PK_NOTIFICATIONS PRIMARY KEY (id));
-- rollback DROP TABLE notifications;

-- changeset auto:main:********-20
CREATE TABLE IF NOT EXISTS notifications_read (id INT AUTO_INCREMENT NOT NULL, uid VARCHAR(32) NOT NULL, nid INT NOT NULL, created_at timestamp NOT NULL, CONSTRAINT PK_NOTIFICATIONS_READ PRIMARY KEY (id));
-- rollback DROP TABLE notifications_read;

-- changeset auto:main:********-21
CREATE TABLE IF NOT EXISTS token_metadata (chain_id VARCHAR(100) NOT NULL, contract_address VARCHAR(100) NOT NULL, coingecko_id VARCHAR(100) NULL, symbol VARCHAR(100) NULL, name VARCHAR(100) NULL, `schema` VARCHAR(100) NOT NULL, CONSTRAINT PK_TOKEN_METADATA PRIMARY KEY (chain_id, contract_address));
-- rollback DROP TABLE token_metadata;

-- changeset auto:main:********-22
CREATE TABLE IF NOT EXISTS tx_block_timestamp (chain_id VARCHAR(20) NOT NULL, block_num INT NOT NULL, unix_timestamp INT NOT NULL, CONSTRAINT PK_TX_BLOCK_TIMESTAMP PRIMARY KEY (chain_id, block_num));
-- rollback DROP TABLE tx_block_timestamp;

-- changeset auto:main:********-23
CREATE TABLE IF NOT EXISTS tx_details (id BIGINT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, tx_hash VARCHAR(100) NOT NULL, tx_timestamp timestamp NOT NULL, category VARCHAR(14) NOT NULL, block_num INT UNSIGNED NOT NULL, from_address VARCHAR(60) NOT NULL, to_address VARCHAR(60) NOT NULL, contract_address VARCHAR(60) NOT NULL, asset VARCHAR(100) NOT NULL, token_id VARCHAR(100) NOT NULL, value VARCHAR(100) NOT NULL, value_decimals TINYINT(3) DEFAULT 0 NOT NULL, value_decimal DECIMAL(20, 8) NOT NULL, gas_price VARCHAR(100) NOT NULL, gas_used VARCHAR(100) NOT NULL, is_error TINYINT(3) DEFAULT 0 NOT NULL, modified_at timestamp NOT NULL, CONSTRAINT PK_TX_DETAILS PRIMARY KEY (id));
-- rollback DROP TABLE tx_details;

-- changeset auto:main:********-24
CREATE TABLE IF NOT EXISTS tx_job_logs (id INT AUTO_INCREMENT NOT NULL, job_id INT NOT NULL, chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, category VARCHAR(14) NOT NULL, from_num INT UNSIGNED DEFAULT 0 NOT NULL, to_num INT UNSIGNED DEFAULT 0 NOT NULL, to_hash VARCHAR(100) NOT NULL, priority INT NOT NULL, data_cnt INT DEFAULT 0 NOT NULL, time_sec FLOAT(12) DEFAULT 0 NOT NULL, status TINYINT(3) DEFAULT 0 NOT NULL, msg VARCHAR(250) NOT NULL, created_at timestamp NOT NULL, CONSTRAINT PK_TX_JOB_LOGS PRIMARY KEY (id));
-- rollback DROP TABLE tx_job_logs;

-- changeset auto:main:********-25
CREATE TABLE IF NOT EXISTS tx_jobs (id INT AUTO_INCREMENT NOT NULL, chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, category VARCHAR(14) NOT NULL, from_num INT UNSIGNED DEFAULT 0 NOT NULL, to_num INT UNSIGNED DEFAULT 0 NOT NULL, to_hash VARCHAR(100) NOT NULL, priority INT NOT NULL, try_after timestamp NOT NULL, created_at timestamp NOT NULL, CONSTRAINT PK_TX_JOBS PRIMARY KEY (id));
-- rollback DROP TABLE tx_jobs;

-- changeset auto:main:********-26
CREATE TABLE IF NOT EXISTS tx_lists (chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, tx_hash VARCHAR(100) NOT NULL, tx_timestamp timestamp NOT NULL, target_address VARCHAR(60) NOT NULL, block_num INT UNSIGNED NOT NULL, tx_type TINYINT(3) DEFAULT 0 NOT NULL, send VARCHAR(100) NULL, receive VARCHAR(100) NULL, fee VARCHAR(100) DEFAULT '0' NOT NULL, fee_decimal DECIMAL(20, 8) DEFAULT 0 NOT NULL, metadata JSON NULL, modified_at timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_TX_LISTS PRIMARY KEY (chain_id, address, tx_hash, tx_timestamp));
-- rollback DROP TABLE tx_lists;

-- changeset auto:main:********-27
CREATE TABLE IF NOT EXISTS tx_updates (chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, category VARCHAR(14) NOT NULL, from_num INT DEFAULT 0 NOT NULL, to_num INT DEFAULT 0 NOT NULL, from_hash VARCHAR(100) NOT NULL, to_hash VARCHAR(100) NOT NULL, created_at timestamp NOT NULL, modified_at timestamp NOT NULL, CONSTRAINT PK_TX_UPDATES PRIMARY KEY (chain_id, address, category));
-- rollback DROP TABLE tx_updates;

-- changeset auto:main:********-28
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = 'dashboard_projects' AND index_name='chain' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_projects ADD CONSTRAINT chain UNIQUE (chain_id, contract_address);
-- rollback ALTER TABLE dashboard_projects DROP CONSTRAINT chain;

-- changeset auto:main:********-29
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='chain' and TABLE_NAME = 'nft_asset_tags' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_asset_tags ADD CONSTRAINT chain UNIQUE (chain_id, contract_address, token_id, uid, tag);
-- rollback ALTER TABLE nft_asset_tags DROP CONSTRAINT chain;

-- changeset auto:main:********-30
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='chain' and TABLE_NAME = 'nft_assets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_assets ADD CONSTRAINT chain UNIQUE (chain_id, contract_address, token_id);
-- rollback ALTER TABLE nft_assets DROP CONSTRAINT chain;

-- changeset auto:main:********-31
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='chain_id' and TABLE_NAME = 'nft_user_amounts' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_user_amounts ADD CONSTRAINT chain_id UNIQUE (chain_id, contract_address, token_id, owner_address);
-- rollback ALTER TABLE nft_user_amounts DROP CONSTRAINT chain_id;

-- changeset auto:main:********-32
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='uid' and TABLE_NAME = 'notifications_read' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE notifications_read ADD CONSTRAINT uid UNIQUE (uid, nid);
-- rollback ALTER TABLE notifications_read DROP CONSTRAINT uid;

-- changeset auto:main:********-33
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_lists' AND index_name='address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX address ON tx_lists(address, chain_id, tx_timestamp);
-- rollback DROP INDEX address ON tx_lists;

-- changeset auto:main:********-34
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_prizes' AND index_name='chain' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain ON dashboard_prizes(chain_id, contract_address);
-- rollback DROP INDEX chain ON dashboard_prizes;

-- changeset auto:main:********-35
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_details' AND index_name='chain_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain_id ON tx_details(chain_id, address, tx_hash, category);
-- rollback DROP INDEX chain_id ON tx_details;

-- changeset auto:main:********-36
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_job_logs' AND index_name='chain_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain_id ON tx_job_logs(chain_id, address, category, created_at);
-- rollback DROP INDEX chain_id ON tx_job_logs;

-- changeset auto:main:********-37
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_jobs' AND index_name='chain_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain_id ON tx_jobs(chain_id, address, priority, category, from_num);
-- rollback DROP INDEX chain_id ON tx_jobs;

-- changeset auto:main:********-38
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_details' AND index_name='chain_id_2' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain_id_2 ON tx_details(chain_id, address, block_num, category);
-- rollback DROP INDEX chain_id_2 ON tx_details;

-- changeset auto:main:********-39
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_jobs' AND index_name='chain_id_2' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain_id_2 ON tx_jobs(chain_id, priority, try_after);
-- rollback DROP INDEX chain_id_2 ON tx_jobs;

-- changeset auto:main:********-40
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='collection_name' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX collection_name ON nft_assets(collection_name);
-- rollback DROP INDEX collection_name ON nft_assets;

-- changeset auto:main:********-41
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_projects' AND index_name='collection_slug' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX collection_slug ON dashboard_projects(collection_slug);
-- rollback DROP INDEX collection_slug ON dashboard_projects;

-- changeset auto:main:********-42
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='collection_slug' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX collection_slug ON nft_assets(collection_slug);
-- rollback DROP INDEX collection_slug ON nft_assets;

-- changeset auto:main:********-43
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='data_contract_metadata' AND index_name='contract_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX contract_address ON data_contract_metadata(contract_address, chain_id);
-- rollback DROP INDEX contract_address ON data_contract_metadata;

-- changeset auto:main:********-44
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='historical_balances' AND index_name='historical_balances_wallet_address_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX historical_balances_wallet_address_IDX ON historical_balances(wallet_address, created_at);
-- rollback DROP INDEX historical_balances_wallet_address_IDX ON historical_balances;

-- changeset auto:main:********-45
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_collections' AND index_name='history' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX history ON nft_collections(history, modified_at, floor_price);
-- rollback DROP INDEX history ON nft_collections;

-- changeset auto:main:********-46
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='name' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX name ON nft_assets(name);
-- rollback DROP INDEX name ON nft_assets;

-- changeset auto:main:********-47
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_prizes' AND index_name='owner_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX owner_address ON dashboard_prizes(owner_address);
-- rollback DROP INDEX owner_address ON dashboard_prizes;

-- changeset auto:main:********-48
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_projects' AND index_name='owner_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX owner_address ON dashboard_projects(owner_address);
-- rollback DROP INDEX owner_address ON dashboard_projects;

-- changeset auto:main:********-49
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_user_amounts' AND index_name='owner_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX owner_address ON nft_user_amounts(owner_address, chain_id, amount);
-- rollback DROP INDEX owner_address ON nft_user_amounts;

-- changeset auto:main:********-50
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_redeem_logs' AND index_name='prize_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX prize_id ON dashboard_redeem_logs(prize_id);
-- rollback DROP INDEX prize_id ON dashboard_redeem_logs;

-- changeset auto:main:********-51
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_prizes' AND index_name='project_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX project_id ON dashboard_prizes(project_id);
-- rollback DROP INDEX project_id ON dashboard_prizes;

-- changeset auto:main:********-52
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_redeem_logs' AND index_name='project_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX project_id ON dashboard_redeem_logs(project_id);
-- rollback DROP INDEX project_id ON dashboard_redeem_logs;

-- changeset auto:main:********-53
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='notifications' AND index_name='receiver' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX receiver ON notifications(receiver, created_at);
-- rollback DROP INDEX receiver ON notifications;

-- changeset auto:main:********-54
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_redeem_logs' AND index_name='redeemed_time' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX redeemed_time ON dashboard_redeem_logs(redeemed_time);
-- rollback DROP INDEX redeemed_time ON dashboard_redeem_logs;

-- changeset auto:main:********-55
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='alchemy_addresses' AND index_name='uid' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX uid ON alchemy_addresses(uid);
-- rollback DROP INDEX uid ON alchemy_addresses;

-- changeset auto:main:********-56
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_asset_tags' AND index_name='uid' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX uid ON nft_asset_tags(uid, tag);
-- rollback DROP INDEX uid ON nft_asset_tags;

-- changeset auto:main:********-57
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='update_retry_cnt' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX update_retry_cnt ON nft_assets(update_retry_cnt, update_retry_after, update_priority);
-- rollback DROP INDEX update_retry_cnt ON nft_assets;

-- changeset auto:main:********-58
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='tx_hash_UNIQUE' and TABLE_NAME = 'airdrop_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE airdrop_logs ADD CONSTRAINT tx_hash_UNIQUE UNIQUE (tx_hash);
-- rollback ALTER TABLE airdrop_logs DROP CONSTRAINT tx_hash_UNIQUE;

-- changeset auto:main:20220913-59
ALTER TABLE historical_balances MODIFY chain_id VARCHAR(100) NULL;
-- rollback ;

-- changeset hunter:KW-163:20220920:1
CREATE TABLE dashboard_prize_redeemed (id INT AUTO_INCREMENT NOT NULL, prize_id INT NOT NULL, project_id INT NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(60) NOT NULL, token_id VARCHAR(100) NOT NULL, redeemed_quantity INT NOT NULL, total_quantity INT NOT NULL, created_at timestamp NOT NULL, modified_at timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_DASHBOARD_PRIZE_REDEEMED PRIMARY KEY (id));
-- rollback DROP TABLE dashboard_prize_redeemed;

-- changeset hunter:KW-163:20220920:2
CREATE TABLE if not exists dashboard_prize_tokens (prize_id INT NOT NULL, token_id VARCHAR(100) NOT NULL, chain_id VARCHAR(20) NOT NULL, contract_address VARCHAR(60) NOT NULL, created_at timestamp NOT NULL, CONSTRAINT PK_DASHBOARD_PRIZE_TOKENS PRIMARY KEY (prize_id, token_id, chain_id, contract_address));
-- rollback DROP TABLE dashboard_prize_tokens;

-- changeset hunter:KW-163:20220920:3
CREATE TABLE  IF NOT EXISTS user_prize_tags (uid VARCHAR(42) NOT NULL, prize_id INT NOT NULL, token_id VARCHAR(100) NOT NULL, tag ENUM('', 'SEEN', 'HIDDEN', 'FAVORITE') NOT NULL, created_at timestamp NOT NULL, CONSTRAINT PK_USER_PRIZE_TAGS PRIMARY KEY (uid, prize_id, token_id, tag));
-- rollback DROP TABLE user_prize_tags;

-- changeset hunter:KW-163:20220920:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='chain_id' and TABLE_NAME = 'dashboard_redeem_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_redeem_logs ADD chain_id VARCHAR(20) NOT NULL;
-- rollback ALTER TABLE dashboard_redeem_logs DROP COLUMN chain_id;

-- changeset hunter:KW-163:20220920:5
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='contract_address' and TABLE_NAME = 'dashboard_redeem_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_redeem_logs ADD contract_address VARCHAR(60) NOT NULL;
-- rollback ALTER TABLE dashboard_redeem_logs DROP COLUMN contract_address;

-- changeset hunter:KW-163:20220920:6
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='verified' and TABLE_NAME = 'dashboard_projects' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_projects ADD verified tinyint(1) DEFAULT 0 NOT NULL;
-- rollback ALTER TABLE dashboard_projects DROP COLUMN verified;

-- changeset hunter:KW-163:20220920:7
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='custom_id' and TABLE_NAME = 'dashboard_prizes' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_prizes ADD custom_id VARCHAR(20) NOT NULL;
-- rollback ALTER TABLE dashboard_prizes DROP COLUMN custom_id;

-- changeset hunter:KW-163:20220920:8
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='matched_trait' and TABLE_NAME = 'dashboard_prizes' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_prizes ADD matched_trait JSON NULL;
-- rollback ALTER TABLE dashboard_prizes DROP COLUMN matched_trait;

-- changeset hunter:KW-163:20220920:9
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='redeem_url' and TABLE_NAME = 'dashboard_prizes' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_prizes ADD redeem_url VARCHAR(250) NULL;
-- rollback ALTER TABLE dashboard_prizes DROP COLUMN redeem_url;

-- changeset hunter:KW-163:20220920:10
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='same_code' and TABLE_NAME = 'dashboard_prizes' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_prizes ADD same_code VARCHAR(60) NULL;
-- rollback ALTER TABLE dashboard_prizes DROP COLUMN same_code;

-- changeset hunter:KW-163:20220920:11
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='prize_id' and TABLE_NAME = 'dashboard_prize_redeemed' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_prize_redeemed ADD CONSTRAINT prize_id UNIQUE (prize_id, chain_id, contract_address, token_id);
-- rollback ALTER TABLE dashboard_prize_redeemed DROP CONSTRAINT prize_id;

-- changeset hunter:KW-163:20220920:12
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_prizes' AND index_name='custom_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX custom_id ON dashboard_prizes(custom_id);
-- rollback DROP INDEX custom_id ON dashboard_prizes;

-- changeset hunter:KW-163:20220920:13
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_prize_tokens' AND index_name='prize_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX prize_id ON dashboard_prize_tokens(prize_id);
-- rollback DROP INDEX prize_id ON dashboard_prize_tokens;

-- changeset hunter:KW-163:20220920:14
ALTER TABLE dashboard_projects MODIFY contract_address VARCHAR(60);
-- rollback ;

-- changeset hunter:KW-163:20220920:15
ALTER TABLE dashboard_redeem_logs MODIFY token_id VARCHAR(100);
-- rollback ;

-- changeset hunter:KW-163:20220920:16
DROP INDEX project_id ON dashboard_prizes;
CREATE INDEX project_id ON dashboard_prizes(project_id, custom_id);
-- rollback ;

-- changeset jason:kw483:20220916-60
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='historical_balances' AND index_name='historical_balances_created_at_wallet_address_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX historical_balances_created_at_wallet_address_IDX ON historical_balances(created_at, wallet_address);
-- rollback DROP INDEX historical_balances_created_at_wallet_address_IDX ON historical_balances;

-- changeset jason:kw483:20220916-61
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='historical_balances' AND index_name='historical_balances_created_at_chain_id_wallet_address_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX historical_balances_created_at_chain_id_wallet_address_IDX ON historical_balances(created_at, chain_id, wallet_address);
-- rollback DROP INDEX historical_balances_created_at_chain_id_wallet_address_IDX ON historical_balances;

-- changeset jason:kw483:20220916-62
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='historical_balances' AND index_name='historical_balances_wallet_address_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
DROP INDEX historical_balances_wallet_address_IDX ON historical_balances;
-- rollback CREATE INDEX historical_balances_wallet_address_IDX ON historical_balances(wallet_address, created_at);

-- changeset jason:kw483:20220916-63
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='assets' AND index_name='assets_chain_id_asset_group_wallet_address_asset_type_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX assets_chain_id_asset_group_wallet_address_asset_type_IDX ON assets(chain_id, asset_group, wallet_address, asset_type);
-- rollback DROP INDEX assets_chain_id_asset_group_wallet_address_asset_type_IDX ON assets;

-- changeset jason:kw483:20220916-64
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='assets' AND index_name='assets_chain_id_wallet_address_asset_type_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX assets_chain_id_wallet_address_asset_type_IDX ON assets(chain_id, wallet_address, asset_type);
-- rollback DROP INDEX assets_chain_id_wallet_address_asset_type_IDX ON assets;

-- changeset hunter:KW-502:20220908:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='thirty_day_volume' and TABLE_NAME = 'nft_collections' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_collections ADD thirty_day_volume DECIMAL(18, 6) UNSIGNED DEFAULT 0 NOT NULL;
-- rollback ALTER TABLE nft_collections DROP COLUMN thirty_day_volume;

-- changeset hunter:KW-502:20220908:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='stats' and TABLE_NAME = 'nft_collections' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_collections ADD stats JSON NULL;
-- rollback ALTER TABLE nft_collections DROP COLUMN stats;

-- changeset hunter:KW-416:20220915:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='method_id' and TABLE_NAME = 'tx_details' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE tx_details ADD method_id VARCHAR(10) NULL;
-- rollback ALTER TABLE tx_details DROP COLUMN method_id;

-- changeset hunter:KW-416:20220915:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='function_name' and TABLE_NAME = 'tx_details' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE tx_details ADD function_name VARCHAR(250) NULL;
-- rollback ALTER TABLE tx_details DROP COLUMN function_name;

-- changeset hunter:KW-416:20220915:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='approve' and TABLE_NAME = 'tx_details' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE tx_details ADD approve SMALLINT DEFAULT 0 NOT NULL;
-- rollback ALTER TABLE tx_details DROP COLUMN approve;

-- changeset hunter:KW-163:20220923:1
ALTER TABLE dashboard_projects CHANGE verified verified TINYINT(1) NOT NULL DEFAULT '0';
-- rollback;

-- changeset jason:KW-559:20221004:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='preview_image_url' and TABLE_NAME = 'notifications' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE notifications ADD preview_image_url varchar(255) NOT NULL;
-- rollback ALTER TABLE notifications DROP COLUMN preview_image_url;

-- changeset jason:KW-532:20220927:1
CREATE TABLE IF NOT EXISTS user_addresses (uid VARCHAR(60) NOT NULL, chain_id VARCHAR(20) NOT NULL, address VARCHAR(60) NOT NULL, CONSTRAINT PK_USER_ADDRESSES PRIMARY KEY (uid, chain_id, address));
-- rollback DROP TABLE user_addresses;

-- changeset jason:KW-532:20220927:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='user_addresses' AND index_name='user_addresses_address_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX user_addresses_address_IDX ON user_addresses(address, chain_id);
-- rollback DROP INDEX user_addresses_address_IDX ON user_addresses;

-- changeset jason:KW-570:20220930:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='animation_url' and TABLE_NAME = 'nft_assets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_assets ADD animation_url varchar(255) NULL;
-- rollback ALTER TABLE nft_assets DROP COLUMN animation_url;

-- changeset jason:KW-626:20221013:1
CREATE TABLE IF NOT EXISTS sms_logs (id int auto_increment NOT NULL, phone_number varchar(100) NOT NULL, code varchar(10) NOT NULL, verified_at TIMESTAMP NULL, created_at TIMESTAMP NOT NULL, CONSTRAINT sms_logs_PK PRIMARY KEY (id));
-- rollback DROP TABLE sms_logs;

-- changeset jason:KW-626:20221013:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='sms_logs' AND index_name='sms_logs_phone_number_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX sms_logs_phone_number_IDX ON sms_logs (phone_number,created_at,code);
-- rollback DROP INDEX sms_logs_phone_number_IDX ON sms_logs;

-- changeset hunter:KW-441:20221007:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='tx_timestamp' and TABLE_NAME = 'nft_user_amounts' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_user_amounts ADD tx_timestamp timestamp NULL;
-- rollback ALTER TABLE nft_user_amounts DROP COLUMN tx_timestamp;

-- changeset alan:KW-614:20221011:1
CREATE TABLE IF NOT EXISTS airdrop_whitelist (id INT AUTO_INCREMENT NOT NULL, event_id VARCHAR(36) NULL, level VARCHAR(24) NULL, phone_number_list JSON NULL, token_id INT NOT NULL, max_supply INT NOT NULL, CONSTRAINT PK_AIRDROP_WHITELIST PRIMARY KEY (id));
-- rollback DROP TABLE airdrop_whitelist;

-- changeset jason:KW-623:20221019:1
CREATE TABLE IF NOT EXISTS idv_task_logs (id int auto_increment NOT NULL, uid varchar(32) NOT NULL, idv_task_id int NOT NULL, idv_task_timestamp TIMESTAMP NOT NULL, created_at TIMESTAMP NOT NULL, message varchar(100) NULL, CONSTRAINT idv_task_logs_PK PRIMARY KEY (id));
-- rollback DROP TABLE idv_task_logs;

-- changeset jason:KW-623:20221019:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='idv_task_logs' AND index_name='idv_task_logs_uid_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idv_task_logs_uid_IDX ON idv_task_logs (uid,created_at);
-- rollback DROP INDEX idv_task_logs_uid_IDX ON idv_task_logs;

-- changeset jason:KW-623:20221021:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='updated_at' and TABLE_NAME = 'idv_task_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE idv_task_logs ADD updated_at TIMESTAMP NOT NULL;
-- rollback ALTER TABLE idv_task_logs DROP COLUMN updated_at;

-- changeset jason:KW-623:20221021:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='search_task_id' and TABLE_NAME = 'idv_task_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE idv_task_logs ADD search_task_id INT NULL;
-- rollback ALTER TABLE idv_task_logs DROP COLUMN search_task_id;

-- changeset jason:KW-623:20221021:3
CREATE TABLE IF NOT EXISTS kyc_results (uid varchar(32) NOT NULL,state int NOT NULL,review_reasons json NULL,reject_reasons json NULL,audit_status varchar(32) NULL,latest_dd_task json NULL,sanction_matched BOOL NULL,potential_risk int NULL,accepted BOOL NULL,comment varchar(100) NULL,CONSTRAINT kyc_results_PK PRIMARY KEY (uid));
-- rollback DROP TABLE kyc_results;

-- changeset alan:DB-34:20221018:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='user_role' and TABLE_NAME = 'dashboard_projects' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_projects ADD user_role ENUM('official', 'unofficial', 'kryptogo') NOT NULL DEFAULT 'official';
-- rollback ;

-- changeset alan:DB-34:20221018:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='user_role' and TABLE_NAME = 'dashboard_prizes' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE dashboard_prizes ADD user_role ENUM('official', 'unofficial', 'kryptogo') NOT NULL DEFAULT 'official';
UPDATE wallet.dashboard_projects SET user_role='kryptogo' WHERE contract_address= '******************************************' and id >1;
UPDATE wallet.dashboard_prizes SET user_role='kryptogo' WHERE contract_address= '******************************************' and id >1;
-- rollback ;

-- changeset alan:DB-34:20221018:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*)>0 FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_projects' AND index_name='chain' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
DROP INDEX chain ON dashboard_projects;
-- rollback ;

-- changeset alan:DB-34:20221018:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_projects' AND index_name='chain' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX chain ON dashboard_projects(chain_id, contract_address);
-- rollback ;
-- changeset jason:KW-623:20221104:1
ALTER TABLE notifications MODIFY COLUMN message_type ENUM('transaction', 'announcement', 'system') NOT NULL;
-- rollback ALTER TABLE notifications MODIFY COLUMN message_type ENUM('transaction', 'announcement') NOT NULL;

-- changeset alan:DB-34:20221018:5
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='dashboard_projects' AND index_name='contract_owner_index' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE UNIQUE INDEX contract_owner_index ON dashboard_projects(chain_id, contract_address, owner_address);
-- rollback ;

-- changeset alan:KW-727:20221108:1
CREATE TABLE IF NOT EXISTS airdrop_nft_traits (event_id varchar(45) NOT NULL, trait_id varchar(45) NOT NULL, attributes JSON,image_url varchar(255) DEFAULT NULL,collection_name varchar(250) DEFAULT NULL, description text, external_url varchar(255) DEFAULT NULL, animation_url varchar(255) DEFAULT NULL,PRIMARY KEY (event_id,trait_id));
-- rollback DROP TABLE airdrop_nft_traits;

-- changeset harry:KW-692:20221114:1
CREATE TABLE IF NOT EXISTS ws_events (request_uuid BINARY(16) NOT NULL, uid VARCHAR(60) NOT NULL, chain_id VARCHAR(20) NOT NULL, jsonrpc_id int NOT NULL, jsonrpc_version VARCHAR(8) NOT NULL, method VARCHAR(60) NOT NULL, params JSON NOT NULL, result JSON NULL, error JSON NULL, canceled TINYINT(1) DEFAULT 0 NOT NULL, created_at timestamp NOT NULL, modified_at timestamp DEFAULT NOW() NOT NULL, CONSTRAINT PK_WS_EVENTS PRIMARY KEY (request_uuid));
-- rollback DROP TABLE ws_events;

-- changeset harry:KW-692:20221114:2
ALTER TABLE dashboard_projects MODIFY contract_address VARCHAR(60) NOT NULL;
-- rollback ;

-- changeset harry:KW-692:20221114:3
ALTER TABLE dashboard_redeem_logs MODIFY token_id VARCHAR(100) NOT NULL;
-- rollback ;

-- changeset alan:KW-738:20221016:1
ALTER TABLE nft_assets ADD collection_description TEXT NULL;
-- rollback ALTER TABLE nft_assets DROP COLUMN collection_description;

-- changeset alan:KW-738:20221016:2
ALTER TABLE nft_assets RENAME COLUMN collection_description to nft_description;
-- rollback ALTER TABLE nft_assets RENAME COLUMN nft_description to collection_description;

-- changeset alan:hotfix:20221201:1
ALTER TABLE airdrop_logs MODIFY event_id VARCHAR(52) NULL;

-- changeset alan:hotfix:20221205:1
ALTER TABLE airdrop_events MODIFY event_id VARCHAR(52) NOT NULL;
-- rollback ;

-- changeset jason:KW-670:20221207:1
CREATE TABLE IF NOT EXISTS orders (
	id varchar(16) NOT NULL,
	uid varchar(60) NOT NULL,
	wallet_address varchar(60) NOT NULL,
	chain_id varchar(20) NOT NULL,
	contract_address varchar(60) NOT NULL,
	amount varchar(100) NOT NULL,
	price varchar(100) NOT NULL,
	network_fee varchar(100) NOT NULL,
	processing_fee varchar(100) NOT NULL,
	total_cost varchar(100) NOT NULL,
	payment_gateway ENUM('stripe','applepay','googlepay') NOT NULL,
	stripe_id varchar(100) NULL,
	created_at TIMESTAMP NOT NULL,
	updated_at TIMESTAMP NOT NULL,
	deleted_at TIMESTAMP NULL,
	CONSTRAINT orders_PK PRIMARY KEY (id)
)
-- rollback DROP TABLE orders;

-- changeset jason:KW-670:20221207:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='orders' AND index_name='orders_uid_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX orders_uid_IDX ON orders (uid,created_at);
-- rollback DROP INDEX orders_uid_IDX ON orders;

-- changeset jason:KW-670:20221207:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='status' and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD status enum('init','pending','done','failed','cancelled') NOT NULL;
-- rollback ALTER TABLE orders DROP COLUMN status;

-- changeset jason:KW-670:20221207:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='binance_withdrawal_id' and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD binance_withdrawal_id varchar(60) NULL;
-- rollback ALTER TABLE orders DROP COLUMN binance_withdrawal_id;

-- changeset jason:KW-670:20221207:5
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='binance_order_id' and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD binance_order_id varchar(100) NULL;
-- rollback ALTER TABLE orders DROP COLUMN binance_order_id;

-- changeset alan:KW-840:20221213:1
CREATE TABLE IF NOT EXISTS `billing_info` (
  `uid` varchar(60) NOT NULL,
  `address` blob NOT NULL,
  `address2` blob,
  `city` blob NOT NULL,
  `post_code` blob NOT NULL,
  `country` blob NOT NULL,
  PRIMARY KEY (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
-- rollback DROP TABLE billing_info;

-- changeset alan:KW-840:20221213:2
CREATE TABLE IF NOT EXISTS `credit_cards` (
  `uid` varchar(60) NOT NULL,
  `number` blob NOT NULL,
  `exp_month` blob NOT NULL,
  `exp_year` blob NOT NULL,
  PRIMARY KEY (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
-- rollback DROP TABLE credit_cards;

-- changeset harry:KW-920:20221214:1
CREATE TABLE IF NOT EXISTS api_keys (id int auto_increment NOT NULL, name varchar(250) NOT NULL, email varchar(250) NOT NULL, key_hash varchar(64) NOT NULL, creator varchar(250) NOT NULL, created_at TIMESTAMP NOT NULL, deleted_at TIMESTAMP, CONSTRAINT api_keys_PK PRIMARY KEY (id), UNIQUE (key_hash));
-- rollback DROP TABLE api_keys;

-- changeset alan:KW-882:20221220:1
CREATE TABLE IF NOT EXISTS crypto_tos_logs (
  id int NOT NULL AUTO_INCREMENT,
  uid varchar(60) NOT NULL,
  version varchar(8) NOT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_crypto_tos_logs_uid (uid)
)
-- rollback DROP TABLE crypto_tos_logs;

-- changeset jason:KW-670:20221212:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='status' and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE
    orders
MODIFY
    status enum(
        'init',
        'pending_for_payment_completed',
        'pending_for_buying_crypto',
        'pending_for_withdrawal',
        'done',
        'failed',
        'cancelled'
    )
NOT NULL;
-- rollback ALTER TABLE orders MODIFY status enum('init','pending','done','failed','cancelled') NOT NULL;

-- changeset jason:KW-670:20221215:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='card_last4' and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD card_last4 varchar(10) NULL;
-- rollback ALTER TABLE orders DROP COLUMN card_last4;

-- changeset jason:KW-670:20221220:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='orders' AND index_name='orders_stripe_id_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX orders_stripe_id_IDX ON orders (stripe_id);
-- rollback DROP INDEX orders_stripe_id_IDX ON orders;

-- changeset alan:KW-692:20230110:1
ALTER TABLE ws_events MODIFY jsonrpc_id BIGINT;
-- rollback ;

-- changeset alan:KW-834:20230316:1
CREATE TABLE `signing_audit_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL,
  `ip` varchar(15) NOT NULL,
  `authorization` text NOT NULL,
  `request_body` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `time` (`created_at`),
  KEY `ip` (`ip`)
)
-- rollback DROP TABLE signing_audit_logs;

-- changeset alan:KW-1279:20230321:1
ALTER TABLE airdrop_whitelist CHANGE COLUMN token_id total_supply int;
-- rollback ALTER TABLE airdrop_whitelist CHANGE COLUMN total_supply token_id int;

-- changeset alan:KW-1279:20230321:2
ALTER TABLE airdrop_events ADD daily_limit int NULL;
-- rollback ALTER TABLE airdrop_events DROP COLUMN daily_limit;

-- changeset alan:KW-1279:20230321:3
ALTER TABLE airdrop_logs ADD token_id int NULL;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN token_id;

-- changeset alan:KW-1279:20230321:4
ALTER TABLE airdrop_logs ADD value int NULL;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN value;

-- changeset alan:KW-1279:20230321:5
ALTER TABLE airdrop_logs ADD gas_price int NULL;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN gas_price;

-- changeset alan:KW-1279:20230321:6
ALTER TABLE airdrop_logs ADD gas int NULL;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN gas;

-- changeset alan:KW-1279:20230321:7
ALTER TABLE airdrop_logs ADD data text NULL;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN data;

-- changeset alan:hotfix:20230407:1
ALTER TABLE dashboard_projects MODIFY description text;
-- rollback ;

-- changeset jason:KW-1338:20230417:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='category' and TABLE_NAME = 'sms_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE sms_logs ADD category varchar(20) NULL;
-- rollback ALTER TABLE sms_logs DROP COLUMN category;

-- changeset jason:KW-1338:20230417:2
CREATE TABLE IF NOT EXISTS email_logs (
  id int auto_increment NOT NULL,
  email varchar(100) NOT NULL,
  code varchar(10) NOT NULL,
  category varchar(20) NOT NULL,
  verified_at TIMESTAMP NULL,
  created_at TIMESTAMP NOT NULL,
  CONSTRAINT email_logs_PK PRIMARY KEY (id)
)
-- rollback DROP TABLE email_logs;

-- changeset jason:KW-1338:20230417:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='email_logs' AND index_name='email_logs_email_IDX' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX email_logs_email_IDX ON email_logs (email,created_at,code);
-- rollback DROP INDEX email_logs_email_IDX ON email_logs;


-- changeset alan:hotfix:20230505:1
ALTER TABLE airdrop_logs DROP INDEX tx_hash_UNIQUE;
-- rollback ALTER TABLE airdrop_logs ADD CONSTRAINT tx_hash_UNIQUE UNIQUE (tx_hash);


-- changeset alan:hotfix:20230505:2
ALTER TABLE airdrop_logs MODIFY tx_hash varchar(66) NULL;
-- rollback ALTER TABLE airdrop_logs MODIFY tx_hash varchar(66) NOT NULL;


-- changeset alan:hotfix:20230505:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='tx_hash_UNIQUE' and TABLE_NAME = 'airdrop_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE airdrop_logs DROP CONSTRAINT tx_hash_UNIQUE;
-- rollback ALTER TABLE airdrop_logs ADD CONSTRAINT tx_hash_UNIQUE UNIQUE (tx_hash);

-- changeset alan:hotfix:20230505:4
ALTER TABLE airdrop_events ADD is_blind_box boolean NULL;
-- rollback ALTER TABLE airdrop_events DROP is_blind_box;

-- changeset alan:KW-1556:20230518:1
CREATE TABLE IF NOT EXISTS backstage_members (
  client_id VARCHAR(64) NOT NULL,
  organization VARCHAR(64) NOT NULL,
  member_id VARCHAR(32) NOT NULL,
  member_name VARCHAR(64) NOT NULL,
  wallet_address VARCHAR(62) NOT NULL,
  role VARCHAR(20) NOT NULL,
  status VARCHAR(20) NOT NULL,
  daily_limit decimal(12,4) NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP,
  PRIMARY KEY (client_id, member_id),
  INDEX idx_organization (organization),
  INDEX idx_wallet_address (wallet_address),
  INDEX idx_member_id (member_id),
  INDEX idx_client_id (client_id)
);
-- rollback DROP TABLE backstage_members;

-- changeset alan:KW-1446:20230522:1
CREATE TABLE backstage_tx_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  organization VARCHAR(64) NOT NULL,
  chain_id VARCHAR(20) NOT NULL,
  contract_address VARCHAR(62) NOT NULL,
  member_id VARCHAR(64) NOT NULL,
  member_address VARCHAR(62) NOT NULL,
  recipient VARCHAR(255) NOT NULL,
  from_address VARCHAR(62) NOT NULL,
  to_address VARCHAR(62) NOT NULL,
  tx_hash VARCHAR(88),
  transfer_time INT NOT NULL,
  status VARCHAR(45),
  amount decimal(12,4) NOT NULL,
  token VARCHAR(16) NOT NULL,
  INDEX idx_organization (organization),
  INDEX idx_chain_id (chain_id),
  INDEX idx_member_id (member_id),
  INDEX idx_recipient (recipient),
  INDEX idx_to_address (to_address),
  INDEX idx_transfer_time (transfer_time),
  INDEX idx_tx_hash (tx_hash),
  INDEX idx_status (status),
  INDEX idx_token (token)
);
-- rollback DROP table backstage_tx_logs;

-- changeset jason:KW-1457:20230517:1
CREATE TABLE IF NOT EXISTS client_users (
  user_uid varchar(60) NOT NULL,
  client_id varchar(50) NOT NULL,
  CONSTRAINT client_users_PK PRIMARY KEY (user_uid, client_id)
)
-- rollback DROP TABLE client_users;

-- changeset jason:KW-1560:20230522:1
CREATE TABLE IF NOT EXISTS backstage_permissions (
  id int auto_increment NOT NULL,
  name varchar(50) NOT NULL,
  CONSTRAINT backstage_permissions_PK PRIMARY KEY (id),
  UNIQUE (name)
)
-- rollback DROP TABLE backstage_permissions;

-- changeset jason:KW-1560:20230522:3
CREATE TABLE IF NOT EXISTS backstage_member_permissions (
  client_id varchar(64) NOT NULL,
  member_id varchar(32) NOT NULL,
  permission_id int NOT NULL,
  CONSTRAINT backstage_member_permissions PRIMARY KEY (client_id, member_id, permission_id),
  FOREIGN KEY (client_id,member_id) REFERENCES backstage_members(client_id,member_id),
  FOREIGN KEY (permission_id) REFERENCES backstage_permissions(id)
)
-- rollback DROP TABLE backstage_member_permissions;

-- changeset jason:KW-1561:20230526:1
ALTER TABLE backstage_members MODIFY COLUMN wallet_address VARCHAR(62);
-- rollback ALTER TABLE backstage_members MODIFY COLUMN wallet_address VARCHAR(60);

-- changeset jason:KW-1615:20230526:1
ALTER TABLE notifications ADD client_id VARCHAR(50) NULL;
-- rollback ALTER TABLE notifications DROP COLUMN client_id;

-- changeset jason:KW-1615:20230530:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='notifications' AND index_name='receiver_client_id_created_at_idx' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX receiver_client_id_created_at_idx ON notifications(receiver, client_id, created_at);
-- rollback DROP INDEX receiver_client_id_created_at_idx ON notifications;

-- changeset jason:hotfix:20230531:1
ALTER TABLE assets MODIFY name VARCHAR(150) NOT NULL;
-- rollback ALTER TABLE assets MODIFY name VARCHAR(100) NOT NULL;


-- changeset alan:KW-1722:20230609:1
ALTER TABLE airdrop_events ADD total_limit int NULL;
-- rollback ALTER TABLE airdrop_events DROP COLUMN total_limit;

-- changeset alan:KW-1722:20230609:2
ALTER TABLE airdrop_events ADD contract_schema_name varchar(10) default "ERC721" NOT NULL;
-- rollback ALTER TABLE airdrop_events DROP COLUMN contract_schema_name;

-- changeset alan:KW-1722:20230609:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='airdrop_logs' AND index_name='idx_airdrop_logs_event_id' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_airdrop_logs_event_id ON airdrop_logs(event_id);
-- rollback DROP INDEX idx_airdrop_logs_event_id ON airdrop_logs;

-- changeset alan:KW-1722:20230609:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='airdrop_logs' AND index_name='idx_airdrop_logs_event_id_nonce' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_airdrop_logs_event_id_nonce ON airdrop_logs(event_id, nonce(16));
-- rollback DROP INDEX idx_airdrop_logs_event_id_nonce ON airdrop_logs;


-- changeset alan:KW-1722:20230609:5
ALTER TABLE airdrop_logs ADD phone_number varchar(20) NULL;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN phone_number;

-- changeset alan:KW-1722:20230609:6
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='airdrop_logs' AND index_name='idx_airdrop_logs_receive_status' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_airdrop_logs_receive_status ON airdrop_logs(event_id, to_address, phone_number, status);
-- rollback DROP INDEX idx_airdrop_logs_receive_status ON airdrop_logs;

-- changeset alan:KW-1722:20230609:7
ALTER TABLE airdrop_logs ADD amount int NOT NULL DEFAULT 1;
-- rollback ALTER TABLE airdrop_logs DROP COLUMN amount;

-- changeset alan:KW-1722:20230609:8
ALTER TABLE airdrop_logs MODIFY COLUMN gas_price bigint;
-- rollback ALTER TABLE airdrop_logs MODIFY COLUMN gas_price int;

-- changeset jason:KW-1842:20230703:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='client_id' and TABLE_NAME = 'sms_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE sms_logs ADD client_id varchar(40) NULL;
-- rollback ALTER TABLE sms_logs DROP COLUMN client_id;

-- changeset jason:KW-1842:20230703:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='client_id' and TABLE_NAME = 'email_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE email_logs ADD client_id varchar(40) NULL;
-- rollback ALTER TABLE email_logs DROP COLUMN client_id;

-- changeset alan:KW-1784:20230725:1
CREATE TABLE IF NOT EXISTS studio_nft_projects (
  project_id int NOT NULL AUTO_INCREMENT,
  organization varchar(64) NOT NULL,
  status varchar(10) NOT NULL,
  collection_image_url text,
  collection_name varchar(250),
  symbol_name VARCHAR(100),
  collection_description text,
  banner_image_url text,
  contract_schema_name VARCHAR(10),
  max_supply int,
  start_time bigint,
  end_time bigint,
  title text,
  subtitle text,
  favicon_image_url text,
  msg_content text,
  publish_status varchar(36),
  verified tinyint(1) DEFAULT false,
  event_id varchar(36),
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at timestamp,
  contract_address varchar(42),
  chain_id varchar(64),

  PRIMARY KEY (project_id),
  INDEX idx_created_at (created_at),
  INDEX idx_collection_name (collection_name),
  INDEX idx_status (status)
);
-- rollback DROP TABLE studio_nft_projects;

-- changeset alan:KW-1872:20230809:1
CREATE TABLE IF NOT EXISTS studio_wallet_projects (
  project_id bigint unsigned NOT NULL AUTO_INCREMENT,
  organization varchar(64) NOT NULL,
  status varchar(10) NOT NULL,
  project_name text NOT NULL,
  project_image text,
  version varchar(32) DEFAULT NULL,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (project_id)
);
-- rollback DROP TABLE studio_wallet_projects;

-- changeset alan:KW-1872:20230809:2
CREATE TABLE IF NOT EXISTS wallet_configs (
  id bigint unsigned NOT NULL AUTO_INCREMENT,
  all_rpcs json DEFAULT NULL,
  evm_rpcs json DEFAULT NULL,
  dapp_supported_rpcs json DEFAULT NULL,
  supported_asset_types json DEFAULT NULL,
  show_nft_sell_btn tinyint(1) DEFAULT NULL,
  show_rewards tinyint(1) DEFAULT NULL,
  show_swap tinyint(1) DEFAULT NULL,
  show_poap tinyint(1) DEFAULT NULL,
  show_kyc tinyint(1) DEFAULT NULL,
  show_explore_tab tinyint(1) DEFAULT NULL,
  supported_locales json DEFAULT NULL,
  custom_tokens json DEFAULT NULL,
  help_center_url text,
  support_email text,
  privacy_and_legal_url text,
  privacy_policy_url text,
  discord_url text,
  twitter_url text,
  telegram_url text,
  explorer_banner_item json DEFAULT NULL,
  explorer_screen_recommend_item json DEFAULT NULL,
  app_icon text,
  splash_screen text,
  get_started_image text,
  platforms json DEFAULT NULL,
  languages json DEFAULT NULL,
  app_store_info json DEFAULT NULL,
  use_default json DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_studio_wallet_projects_config FOREIGN KEY (id) REFERENCES studio_wallet_projects (project_id)

);
-- rollback DROP TABLE wallet_configs;

-- changeset alan:KW-1872:20230809:3
CREATE TABLE IF NOT EXISTS wallet_themes (
  id bigint unsigned NOT NULL AUTO_INCREMENT,
  primary_color varchar(7) DEFAULT NULL,
  secondary_color varchar(7) DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_studio_wallet_projects_theme FOREIGN KEY (id) REFERENCES studio_wallet_projects (project_id)
);
-- rollback DROP TABLE wallet_themes;

-- changeset jason:KW-2028:20230817:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME='is_spam' and TABLE_NAME = 'nft_assets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_assets ADD is_spam tinyint(1) DEFAULT NULL;
-- rollback ALTER TABLE nft_assets DROP COLUMN is_spam;

-- changeset jason:KW-2045:20230824:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='idx_nft_assets_is_spam' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_assets_is_spam ON nft_assets(is_spam);
-- rollback DROP INDEX idx_nft_assets_is_spam ON nft_assets;

-- changeset alan:fix:20230829:1
ALTER TABLE airdrop_events ADD subtitle text NULL;
-- rollback ALTER TABLE airdrop_events DROP COLUMN subtitle;

-- changeset jason:hotfix-mysql-perf:20230828:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='alchemy_addresses' AND index_name='idx_chain_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_chain_address ON alchemy_addresses(address, chain_id);
-- rollback DROP INDEX idx_chain_address ON alchemy_addresses;

-- changeset jason:KW-2195:20230914:1
CREATE TABLE IF NOT EXISTS `oauth_client_configs` (
    `id` VARCHAR(50) PRIMARY KEY,
    `domain` VARCHAR(255) NOT NULL,
    `is_privileged` TINYINT(1) NOT NULL,
    `name` VARCHAR(50) NOT NULL,
    `secret_env_var` VARCHAR(50) NOT NULL,
    `support_address` VARCHAR(255) NOT NULL,
    `main_logo` VARCHAR(255) NOT NULL,
    `app_store_link` VARCHAR(255) NOT NULL,
    `google_play_link` VARCHAR(255) NOT NULL,
    `sendbird_app_id` VARCHAR(50) NOT NULL,
    `sendbird_api_token_name` VARCHAR(50) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` TIMESTAMP
);
-- rollback DROP TABLE oauth_client_configs;

-- changeset jason:KW-2195-dev:20230914:2 context:dev
INSERT INTO oauth_client_configs (id, domain, is_privileged, name, secret_env_var, support_address, main_logo, app_store_link, google_play_link, sendbird_app_id, sendbird_api_token_name) VALUES
('9e4ec34a3a39394a', 'https://wallet-api-dev.stickeygo.com', 1, 'Stickey', 'CLIENT_SECRET_STICKEY', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', '', '', '8E923858-8E5C-469D-A781-17D4C2A430B6', 'SENDBIRD_STICKEY_API_TOKEN'),
('8e3dbbd857c9ad8a68a97cbc75ff40ac', 'https://wallet-dev.kryptogo.app', 1, 'Tag', 'CLIENT_SECRET_ABSOLUTE', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/Logo-TAG.png', '', '', '', ''),
('9c5a79fc1117310f976b53752659b61d', 'https://wallet-dev.kryptogo.app', 1, 'KryptoGO', 'CLIENT_SECRET_KRYPTOGO', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', 'https://apps.apple.com/tw/app/kryptogo/id1593830910', 'https://play.google.com/store/apps/details?id=com.kryptogo.walletapp', '8CFF385E-41C7-4A0E-866E-9A3CA16C318B', 'SENDBIRD_KRYPTOGO_API_TOKEN');
-- rollback DELETE FROM oauth_client_configs WHERE id IN ('9e4ec34a3a39394a', '8e3dbbd857c9ad8a68a97cbc75ff40ac', '9c5a79fc1117310f976b53752659b61d');

-- changeset jason:KW-2195-local:20230914:2 context:local
INSERT INTO oauth_client_configs (id, domain, is_privileged, name, secret_env_var, support_address, main_logo, app_store_link, google_play_link, sendbird_app_id, sendbird_api_token_name) VALUES
('************************', 'http://localhost:8040', 1, 'Stickey', 'CLIENT_SECRET_STICKEY', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', '', '', '', 'SENDBIRD_STICKEY_API_TOKEN'),
('de85d62906d71fd320991a3ae83233d6', 'http://localhost:8040', 1, 'Tag', 'CLIENT_SECRET_ABSOLUTE', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/Logo-TAG.png', '', '', '', ''),
('20991a3ae83233d6de85d62906d71fd3', 'http://localhost:8040', 1, 'KryptoGO', 'CLIENT_SECRET_KRYPTOGO', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', 'https://apps.apple.com/tw/app/kryptogo/id1593830910', 'https://play.google.com/store/apps/details?id=com.kryptogo.walletapp', 'sendbird-test-app-id', 'SENDBIRD_KRYPTOGO_API_TOKEN');
-- rollback DELETE FROM oauth_client_configs WHERE id IN ('************************', 'de85d62906d71fd320991a3ae83233d6', '20991a3ae83233d6de85d62906d71fd3');

-- changeset jason:KW-2195-prod:20230914:2 context:prod
INSERT INTO oauth_client_configs (id, domain, is_privileged, name, secret_env_var, support_address, main_logo, app_store_link, google_play_link, sendbird_app_id, sendbird_api_token_name) VALUES
('4b2108adfdb5401a', 'https://wallet-api.stickeygo.com', 1, 'Stickey', 'CLIENT_SECRET_STICKEY', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', '', '', '88D0BF66-2792-42E7-A3FC-0A7344B0C3AF', 'SENDBIRD_STICKEY_API_TOKEN'),
('0c11772efe921d67a5ae56d5ba596a32', 'https://wallet.kryptogo.app', 1, 'Tag', 'CLIENT_SECRET_ABSOLUTE', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/Logo-TAG.png', '', '', '', ''),
('20b1905704bf329be7af231723fe30e3', 'https://wallet.kryptogo.app', 1, 'KryptoGO', 'CLIENT_SECRET_KRYPTOGO', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', 'https://apps.apple.com/tw/app/kryptogo/id1593830910', 'https://play.google.com/store/apps/details?id=com.kryptogo.walletapp', '6D2C050E-C3CC-4B1F-8754-D7DA6E5A604B', 'SENDBIRD_KRYPTOGO_API_TOKEN');
-- rollback DELETE FROM oauth_client_configs WHERE id IN ('4b2108adfdb5401a', '0c11772efe921d67a5ae56d5ba596a32', '20b1905704bf329be7af231723fe30e3');

-- changeset jason:KW-2195-staging:20230914:2 context:staging
INSERT INTO oauth_client_configs (id, domain, is_privileged, name, secret_env_var, support_address, main_logo, app_store_link, google_play_link, sendbird_app_id, sendbird_api_token_name) VALUES
('5fb177d395036039', 'https://wallet-api-staging.stickeygo.com', 1, 'Stickey', 'CLIENT_SECRET_STICKEY', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', '', '', '530B7BF2-9F44-4AB2-AC5B-0742C0358303', 'SENDBIRD_STICKEY_API_TOKEN'),
('1d3c1e149dcf03e015c1cc42caabe083', 'https://wallet-staging.kryptogo.app', 1, 'Tag', 'CLIENT_SECRET_ABSOLUTE', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/Logo-TAG.png', '', '', '', ''),
('7c8117912ec55102331994275ea3f3a3', 'https://wallet-staging.kryptogo.app', 1, 'KryptoGO', 'CLIENT_SECRET_KRYPTOGO', '<EMAIL>', 'https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png', 'https://apps.apple.com/tw/app/kryptogo/id1593830910', 'https://play.google.com/store/apps/details?id=com.kryptogo.walletapp', '757289D3-23B1-4598-A1C5-24244D27045F', 'SENDBIRD_KRYPTOGO_API_TOKEN');
-- rollback DELETE FROM oauth_client_configs WHERE id IN ('5fb177d395036039', '1d3c1e149dcf03e015c1cc42caabe083', '7c8117912ec55102331994275ea3f3a3');

-- changeset harry:KW-2277:20230929:1
CREATE TABLE IF NOT EXISTS `studio_organizations` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    sign_alert_threshold DECIMAL(16, 6) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);
-- rollback DROP TABLE studio_organizations;

-- changeset harry:KW-2277:20230929:2
CREATE TABLE IF NOT EXISTS `studio_organization_wallets` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organization_id INT NOT NULL,
    wallet_type ENUM('evm', 'tron') NOT NULL,
    wallet_address VARCHAR(255) NOT NULL,
    encrypted_private_key VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uniq_org_id_wallet_type (organization_id, wallet_type),
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
    INDEX idx_organization_id (organization_id)
);
-- rollback DROP TABLE studio_organization_wallets;

-- changeset alan:KW-2334:20231013:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='name' and TABLE_NAME = 'studio_organizations' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organizations ADD CONSTRAINT name UNIQUE (name);
-- rollback ALTER TABLE studio_organizations DROP CONSTRAINT name;

-- changeset alan:KW-2334:20231013:2
CREATE TABLE IF NOT EXISTS `studio_users` (
  `organization_id` int NOT NULL,
  `uid` varchar(60) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`organization_id`,`uid`),
  FOREIGN KEY (`organization_id`) REFERENCES studio_organizations(`id`)
);
-- rollback DROP TABLE studio_users;

-- changeset jason:KW-2330:20231012:1
CREATE TABLE IF NOT EXISTS `customers` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organization_id INT NOT NULL,
    uid VARCHAR(60) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uniq_org_id_uid (organization_id, uid),
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
    INDEX idx_org_id_customer (organization_id),
    INDEX idx_uid_customer (uid)
);
-- rollback DROP TABLE customers;

-- changeset jason:KW-2330:20231012:2
CREATE TABLE IF NOT EXISTS `studio_organization_clients` (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organization_id INT NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uniq_studio_organization_clients (organization_id, client_id),
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
    INDEX idx_org_id_studio_organization_client (organization_id),
    INDEX idx_client_id_studio_organization_client (client_id)
);
-- rollback DROP TABLE studio_organization_clients;

-- changeset alan:fix:20231018:1
CREATE TABLE IF NOT EXISTS `studio_organization_modules` (
  `organization_id` int NOT NULL AUTO_INCREMENT,
  `user_360` set('data','engage','audience') DEFAULT NULL,
  `wallet_builder` set('project','configuration','app_publish','marketing_tools') DEFAULT NULL,
  `asset_pro` set('treasury','send_token','transaction_history') DEFAULT NULL,
  `nft_boost` set('campaign','reward') DEFAULT NULL,
  `compliance` set('create_a_task','case_management','all_tasks') DEFAULT NULL,
  `admin` set('members','settings','billing') DEFAULT NULL,
  PRIMARY KEY (`organization_id`)
)
-- rollback DROP TABLE studio_organization_clients;

-- changeset alan:otc-seed:20231018:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_organizations WHERE name IN ('KryptoGO', 'Tag');
INSERT INTO `wallet`.`studio_organizations`
(`name`, `sign_alert_threshold`) VALUES ('KryptoGO', 1.000000), ('Tag', 1.000000);
-- rollback DELETE FROM studio_organizations WHERE name IN ('KryptoGO', 'Tag');

-- changeset alan:otc-seed:20231018:2  context:dev
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_users WHERE organization_id IN (1, 2);
-- kg:jason, tag: jason
INSERT INTO `wallet`.`studio_users`
(`organization_id`, `uid`) VALUES (1, '47YaaBsEXyUTG6r972PSWvrW5JP2'),(2, '47YaaBsEXyUTG6r972PSWvrW5JP2');
-- rollback DELETE FROM studio_users WHERE (organization_id=1 AND uid='47YaaBsEXyUTG6r972PSWvrW5JP2') OR (organization_id=2 AND uid='47YaaBsEXyUTG6r972PSWvrW5JP2');

-- changeset alan:otc-seed:20231018:2  context:staging
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_users WHERE organization_id IN (1, 2);
-- kg:harry, tag: harry
INSERT INTO `wallet`.`studio_users`
(`organization_id`, `uid`) VALUES (1, 'vhwMc3Haj9RAV0EYM9sNSo2mXKE3'),(2, 'vhwMc3Haj9RAV0EYM9sNSo2mXKE3');
-- rollback DELETE FROM studio_users WHERE (organization_id=1 AND uid='vhwMc3Haj9RAV0EYM9sNSo2mXKE3') OR (organization_id=2 AND uid='vhwMc3Haj9RAV0EYM9sNSo2mXKE3');


-- changeset alan:otc-seed:20231018:2  context:prod
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_users WHERE organization_id IN (1, 2);
-- kg:harry, tag: harry
INSERT INTO `wallet`.`studio_users`
(`organization_id`, `uid`) VALUES (1, 'wht9AqlksrOIChOYE3cJChDv3o63'),(2, 'wht9AqlksrOIChOYE3cJChDv3o63');
-- rollback DELETE FROM studio_users WHERE (organization_id=1 AND uid='wht9AqlksrOIChOYE3cJChDv3o63') OR (organization_id=2 AND uid='wht9AqlksrOIChOYE3cJChDv3o63');


-- changeset alan:otc-seed:20231018:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_organization_modules WHERE organization_id IN (1, 2);
INSERT INTO `wallet`.`studio_organization_modules`
(`organization_id`, `user_360`, `wallet_builder`, `asset_pro`, `nft_boost`, `compliance`, `admin`)
VALUES ('1', 'data,engage,audience', 'project,configuration,app_publish,marketing_tools', 'treasury,send_token,transaction_history', 'campaign,reward', 'create_a_task,case_management,all_tasks', 'members,settings,billing')
,('2', '', '', 'send_token,transaction_history', '', '', '');
-- rollback DELETE FROM studio_organization_modules WHERE organization_id IN (1, 2);

-- changeset alan:otc-seed:20231018:4  context:dev
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_organization_wallets WHERE organization_id IN (1, 2);
INSERT INTO `wallet`.`studio_organization_wallets`
(`organization_id`, `wallet_type`, `wallet_address`, `encrypted_private_key`)
VALUES (1, 'evm', '******************************************', 'CiQAfaoiUfTYF7skgshabMj0ECNVkBuLd1q4pKL+8P4Kho85yqISaQDQ7c7ncL6jMUD9+kdW5BcrJHC3u3fCGMxBVg62LoDRleW1C6EVTERdwUdKY2xDubFC3qils0rJjJIfEqQiI0lirqM1pBILy1+J12CNRKBoqMqePYal7yI5VrwnWPlvX7I4+YqKLICgWQ=='),
(1, 'tron', 'TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb', 'CiQAfaoiUYpLoOiEKzsksbs543sNu0SlUKb89HK5epGBbFZ4RAgSaQCnax8p4/mDMHicXpXsSvx9Rwrto9AoVzh2B0d/6oOU+OeQXgsISWM4HRdRbZqa32j0WNlQylsmkPruZ7QKsE3Ei3EaGqvfdL9EtrGRyExtvKSzBQSi8ztZdLDaANwmsczHMkrk6Sv9/Q=='),
(2, 'evm', '******************************************', 'CiQAfaoiUUV6yMqvxJ2MK4Mv6lb6OYxj4TiehXA9C261NPDvZV0SaQCnax8pqt+PE/FT0NP855qAbjOaW1ra6vkUPjjyq7OdGdTQYJqtMxrmZOAAFIu7gYKIXnrug2zjdxMuA2aEVcTER8nvckWiBN6mzEHjMjLGelxWdivgYrzrmOz7aabeGtVRgq5vBtJZRQ=='),
(2, 'tron', 'TUvZQyZpamL4AeMHwwVR3SPivX4iNSFJEK', 'CiQAfaoiUZSGCru6pIDxbbEoqKnyT+NoIvwcld/W5TzGCGHe4V8SaQCnax8pQPc3ENTYu/keyha3ipsfNdJFUPNmEbu/MnKVdOg3TC5FE6EEngQwqxASLdcRt4cgo45BTFEmrgOjYW0z0Xm9x8o4upVRu/V1dVpWFR8uhKsY0d4MQrU+8VOInSCBfKENMuppGg==')
-- rollback DELETE FROM studio_organization_wallets WHERE (organization_id=1 AND wallet_type='evm') OR (organization_id=1 and wallet_type='tron') OR  (organization_id=2 AND wallet_type='evm') OR (organization_id=2 and wallet_type='tron');

-- changeset alan:otc-seed:20231018:4  context:staging
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_organization_wallets WHERE organization_id IN (1, 2);
INSERT INTO `wallet`.`studio_organization_wallets`
(`organization_id`, `wallet_type`, `wallet_address`, `encrypted_private_key`)
VALUES (1, 'evm', '******************************************', 'CiQA5PE/L75vxwsdncYLB8zxNgE2X58KigSKWCMwjqpBKg3TFDQSaQCt34u8uXwTvrV2M86L6zE+EK7oqSyTq/sSlfh6rptuS/Yv63EVBmlO0PC/je3g0m3f3EWuAU4ICwo7Yga8dQWS9Kuen3n00GwdsnB0gDvwfqEF2V7cW1mzS6LPxpr1VRbDqC4XShxP5Q=='),
(1, 'tron', 'TKgUu6NdVwmCXw2UcqJgMb95umX1hjxzhs', 'CiQA5PE/L/S1fr5K1vs6Jz/Z1cSa+j9e8GY74XZmRJfsh93aagUSaQCt34u8xL6ec92Fb8D3IIXr8umd1BIrWXHJ+m7kOA6fYSgoz3oFl3MLDMXdZCFqmgUSUVN9inDCyt7vKsClkOU0XQvY/22Cxv8KnnZPW7ahM8Am4XAFsF9iOm4Ljdu9Nf4PITdaVd5diw=='),
(2, 'evm', '******************************************', 'CiQA5PE/L2oStqU5a5tAqY0J30tba7nAAbDInnHbA6X7+sUme6kSaQCt34u8hc6Fo+E36H9rgW5/4GoNrXYdV43zaajbnDD/hTnvEWg8nCZ51et6WXBC3S8g1UPF01tSSJiDg5wt4jvq+SJquTPoCfDa0r0jxKYaJCiFF8jc4r1Lmvkjvy7jzVXZTVNbVu4DBA=='),
(2, 'tron', 'TZJfb1GinTcJj7o7KsrMjsoJg8EevsFNa9', 'CiQA5PE/L9Ry8melnhVjzl3KUjNIY66lxXbllX917EbupQieubgSaACt34u8dxoNRCO+aof1pi/V+0Uqb5FBUz187Npwb8iV06mQkF4WSyUnYFUqKAW3Z++rnRVCk7shImEUqhpPxVQ0WeSap0Ew0HCLefrnsSi6Q6D8HoSXdBvTg9HLez+lnKkZUY0rn4Gy')
-- rollback DELETE FROM studio_organization_wallets WHERE (organization_id=1 AND wallet_type='evm') OR (organization_id=1 and wallet_type='tron') OR  (organization_id=2 AND wallet_type='evm') OR (organization_id=2 and wallet_type='tron');

-- changeset alan:otc-seed:20231018:4  context:prod
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_organization_wallets WHERE organization_id IN (1, 2);
INSERT INTO `wallet`.`studio_organization_wallets`
(`organization_id`, `wallet_type`, `wallet_address`, `encrypted_private_key`)
VALUES (1, 'evm', '******************************************', 'CiQAOeqwAtaqO9TZ2YLJTMmdmy+suYzclc5kO7H4CEzOElHFZc0SaQBURSkiAJ4u725ZMhxJE+5ITEvhqAGlnXLmrkqtIs1GJZ3lVxxKDBHlPTll1aneKDjLSi62e5RCqNt1d0oXhv8xV7banudMjILQPzwa9QXvTLxZiDQcGObkWTOwPn4sBH7K72c28QCI0Q=='),
(1, 'tron', 'TSSH8dtg57tjJPaXQgpncbsmWHwfAjArUR', 'CiQAOeqwAgjIm12/UQXO9xDqapIQ3J/1C1HQE+fHr/VUP9iavOESaQBURSkiZggfgHNfLM6kgA9qsmOnHe1OUIeDrkp2gvXFB2EfkkPeCYYjyOonTqOeq4vGfdXuoDpYl0tE581qxcNngAbLtrcl/7u28F46JmkT5PzCcGwlJ+Sdsv/8YN6bAM5Xvx/MxypseA=='),
(2, 'evm', '******************************************', 'CiQAOeqwAkCVPwM3S6E/gB+jViB8k+OTjm2GY9CbmN9LHbiKb+wSaQBURSki6XzdZallCeM9heFOlOemEd4Tn63+qGg4r2M2gfo+BskcvnzTLbDsr8sHacCgEPGN7Fn03RS1jTOQoH0Ab3EZyWmwbqtrjhqq8k0LvicgnWHfMQop0FaXhvANKtQlx/lORWs0rA=='),
(2, 'tron', 'TQC1dFNKcpvCaszkjQtpAKWxvyperBi8SF', 'CiQAOeqwAvDn6tkqdJV6lPy2Jid+itFtbZHf22qDVZLssT8h6RESaQBURSkinxsY9Xcj7ZjR6JMwtErgEiNb4pafN7MoFDoFSa07H3stYsq1WUAbCZCc/FxYu2RaZwNPHScqGyr8DHt5yamGijq4IlXj+aR/Hgd0OVXRHoDMbjFXplmj69Q4ZuAWXwYAdDQP4g==')
-- rollback DELETE FROM studio_organization_wallets WHERE (organization_id=1 AND wallet_type='evm') OR (organization_id=1 and wallet_type='tron') OR  (organization_id=2 AND wallet_type='evm') OR (organization_id=2 and wallet_type='tron');

-- changeset jason:hotfix-otc-seed:20231021
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_organizations WHERE name IN ('Stickey');
INSERT INTO `wallet`.`studio_organizations`
(`name`, `sign_alert_threshold`) VALUES ('Stickey', 1.000000);
-- rollback DELETE FROM studio_organizations WHERE name IN ('Stickey');

-- changeset jason:otc-seed:20231018:5 context:dev
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM studio_organization_clients WHERE client_id IN ('8e3dbbd857c9ad8a68a97cbc75ff40ac', '9c5a79fc1117310f976b53752659b61d', '9e4ec34a3a39394a');
INSERT INTO `wallet`.`studio_organization_clients`
(`organization_id`, `client_id`)
VALUES (1, '9c5a79fc1117310f976b53752659b61d'), (2, '8e3dbbd857c9ad8a68a97cbc75ff40ac'), (3, '9e4ec34a3a39394a');
-- rollback DELETE FROM studio_organization_clients WHERE client_id IN ('9c5a79fc1117310f976b53752659b61d', '8e3dbbd857c9ad8a68a97cbc75ff40ac', '9e4ec34a3a39394a');

-- changeset jason:otc-seed:20231018:5 context:prod
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM studio_organization_clients WHERE client_id IN ('0c11772efe921d67a5ae56d5ba596a32', '20b1905704bf329be7af231723fe30e3', '4b2108adfdb5401a');
INSERT INTO `wallet`.`studio_organization_clients`
(`organization_id`, `client_id`)
VALUES (1, '20b1905704bf329be7af231723fe30e3'), (2, '0c11772efe921d67a5ae56d5ba596a32'), (3, '4b2108adfdb5401a');
-- rollback DELETE FROM studio_organization_clients WHERE client_id IN ('20b1905704bf329be7af231723fe30e3', '0c11772efe921d67a5ae56d5ba596a32', '4b2108adfdb5401a');

-- changeset jason:otc-seed:20231018:5 context:staging
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM studio_organization_clients WHERE client_id IN ('1d3c1e149dcf03e015c1cc42caabe083', '7c8117912ec55102331994275ea3f3a3', '5fb177d395036039');
INSERT INTO `wallet`.`studio_organization_clients`
(`organization_id`, `client_id`)
VALUES (1, '7c8117912ec55102331994275ea3f3a3'), (2, '1d3c1e149dcf03e015c1cc42caabe083'), (3, '5fb177d395036039');
-- rollback DELETE FROM studio_organization_clients WHERE client_id IN ('7c8117912ec55102331994275ea3f3a3', '1d3c1e149dcf03e015c1cc42caabe083', '5fb177d395036039');

-- changeset jason:KW-2330:20231012:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='uniq_studio_organization_clients' and TABLE_NAME = 'studio_organization_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_clients DROP CONSTRAINT uniq_studio_organization_clients;
-- rollback ALTER TABLE studio_organization_clients ADD CONSTRAINT uniq_studio_organization_clients UNIQUE (organization_id, client_id);

-- changeset jason:KW-2330:20231012:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='uniq_client_id' and TABLE_NAME = 'studio_organization_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_clients ADD CONSTRAINT uniq_client_id UNIQUE (client_id);
-- rollback ALTER TABLE studio_organization_clients DROP CONSTRAINT uniq_client_id;


-- changeset raiven:KW-2410:20231024:1
CREATE TABLE IF NOT EXISTS `studio_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `module` varchar(32) NOT NULL,
  `name` varchar(32) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_studio_role_module` (`module`,`name`)
)
-- rollback DROP TABLE studio_roles;

-- changeset raiven:KW-2410:20231024:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_roles WHERE (module, name) IN (('', 'owner'),('user_360', 'admin'),('wallet_builder', 'admin'),('asset_pro', 'admin'),('asset_pro', 'approver'),('asset_pro', 'trader'),('nft_boost', 'admin'),('compliance', 'admin'));
INSERT INTO `wallet`.`studio_roles` (`module`, `name`) VALUES
('', 'owner'),
('user_360', 'admin'),
('wallet_builder', 'admin'),
('asset_pro', 'admin'),
('asset_pro', 'approver'),
('asset_pro', 'trader'),
('nft_boost', 'admin'),
('compliance', 'admin');
-- rollback DELETE FROM studio_roles WHERE (module, name) IN (('', 'owner'),('user_360', 'admin'),('wallet_builder', 'admin'),('asset_pro', 'admin'),('asset_pro', 'approver'),('asset_pro', 'trader'),('nft_boost', 'admin'),('compliance', 'admin'));

-- changeset raiven:KW-2412:20231024:1
CREATE TABLE IF NOT EXISTS `studio_role_bindings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `organization_id` int NOT NULL,
  `uid` varchar(60) NOT NULL,
  `role_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_studio_role_binding` (`organization_id`,`uid`,`role_id`),
  FOREIGN KEY (organization_id,uid) REFERENCES studio_users(organization_id,uid),
  FOREIGN KEY (role_id) REFERENCES studio_roles(id)
)
-- rollback DROP TABLE studio_role_bindings;

-- changeset alan:KW-2404:20231024:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('status','invited_at', 'name', 'member_id')) and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_users ADD status ENUM('pending', 'active', 'inactive') NOT NULL DEFAULT 'active';
ALTER TABLE studio_users ADD invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE studio_users ADD name varchar(30) NOT NULL;
ALTER TABLE studio_users ADD member_id varchar(30);
-- rollback ALTER TABLE studio_users DROP COLUMN status, DROP COLUMN invited_at, DROP COLUMN name, DROP COLUMN member_id;


-- changeset alan:KW-2406:20231101:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('email')) and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_users ADD email varchar(100);
-- rollback ALTER TABLE studio_users DROP COLUMN email;

-- changeset jason:KW-2345:20231106:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('square_logo')) and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs ADD square_logo VARCHAR(255);
UPDATE oauth_client_configs SET square_logo = 'https://wallet-static.kryptogo.com/public/icon/KryptoGO%20Demo-24.png' WHERE name = 'KryptoGO Demo';
UPDATE oauth_client_configs SET square_logo = 'https://wallet-static.kryptogo.com/public/icon/KryptoGO-24.png' WHERE name = 'KryptoGO';
UPDATE oauth_client_configs SET square_logo = 'https://wallet-static.kryptogo.com/public/icon/Tag-24.png' WHERE name = 'Tag';
UPDATE oauth_client_configs SET square_logo = 'https://wallet-static.kryptogo.com/public/icon/Stickey-24.png' WHERE name = 'Stickey';
-- rollback ALTER TABLE oauth_client_configs DROP COLUMN square_logo;

-- changeset raiven:KW-2510:20231114:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('daily_transfer_limit', 'transfer_approval_threshold')) and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_users ADD daily_transfer_limit decimal(30,4) UNSIGNED DEFAULT 0 NOT NULL;
ALTER TABLE studio_users ADD transfer_approval_threshold decimal(30,4) UNSIGNED DEFAULT 0 NOT NULL;
-- rollback ALTER TABLE studio_users DROP COLUMN daily_transfer_limit, transfer_approval_threshold;

-- changeset alan:KW-2515:20231114:1
CREATE TABLE IF NOT EXISTS assetpro_tx_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  organization_id INT NOT NULL,
  operator_uid VARCHAR(60) NOT NULL,
  token VARCHAR(16) NOT NULL,
  submit_time INT NOT NULL,
  status ENUM('init','pending','send_success','send_failed') NOT NULL,
  chain_id VARCHAR(20) NOT NULL,
  contract_address VARCHAR(62) NOT NULL,
  from_address VARCHAR(62) NOT NULL,
  to_address VARCHAR(62) NOT NULL,
  tx_hash VARCHAR(66),
  transfer_time INT NULL,
  amount DECIMAL(12,4) NOT NULL,
  recipient_name VARCHAR(255),
  recipient_phone VARCHAR(20),
  recipient_email VARCHAR(255),
  recipient_kyc_status ENUM('rejected','verified','unverified','pending'),
  FOREIGN KEY (organization_id, operator_uid) REFERENCES studio_users (organization_id, uid),
  INDEX idx_organization_id (organization_id),
  INDEX idx_status (status),
  INDEX idx_amount (amount),
  INDEX idx_token (token),
  INDEX idx_transfer_time (transfer_time),
  INDEX idx_chain_id (chain_id),
  INDEX idx_recipient_name (recipient_name),
  INDEX idx_recipient_phone (recipient_phone),
  INDEX idx_recipient_email (recipient_email),
  INDEX idx_to_address (to_address)
);
-- rollback DROP TABLE assetpro_tx_logs;

-- changeset raiven:KW-2510:20231117:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'daily_transfer_limit') and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_users MODIFY COLUMN daily_transfer_limit decimal(30,4) UNSIGNED NULL;
-- rollback ALTER TABLE studio_users MODIFY COLUMN daily_transfer_limit decimal(30,4) UNSIGNED DEFAULT 0 NOT NULL;

-- changeset raiven:KW-2510:20231117:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'transfer_approval_threshold') and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_users MODIFY COLUMN transfer_approval_threshold decimal(30,4) UNSIGNED NULL;
-- rollback ALTER TABLE studio_users MODIFY COLUMN transfer_approval_threshold decimal(30,4) UNSIGNED DEFAULT 0 NOT NULL;

-- changeset raiven:KW-2509:20231117:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'asset_pro') and TABLE_NAME = 'studio_organization_modules' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_modules MODIFY COLUMN asset_pro set('treasury','send_token','transaction_history','operators') DEFAULT NULL;
-- rollback ALTER TABLE studio_organization_modules MODIFY COLUMN asset_pro set('treasury','send_token','transaction_history') DEFAULT NULL;

-- changeset beans:KW-2421:********:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'studio_linebot_config' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE IF NOT EXISTS `studio_linebot_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `organization_id` int NOT NULL,
  `channel_id` varchar(16),
  `channel_secret` varchar(60),
  `channel_access_token` varchar(256),
  `enabled` tinyint(1) NOT NULL DEFAULT 0,
  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'inactive',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_line_account` (`organization_id`),
  UNIQUE KEY `uniq_line_config` (`channel_id`),
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id)
)
-- rollback DROP TABLE studio_linebot_config;

-- changeset beans:KW-2421:********:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'linebot_user_map' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE IF NOT EXISTS `linebot_user_map` (
  `id` int NOT NULL AUTO_INCREMENT,
  `channel_id` varchar(16) NOT NULL,
  `line_user_id` varchar(60) NOT NULL,
  `organization_id` int NOT NULL,
  `customer_id` varchar(60) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_org_line_user` (`channel_id`, `line_user_id`),
  FOREIGN KEY (channel_id) REFERENCES studio_linebot_config(channel_id),
  FOREIGN KEY (organization_id, customer_id) REFERENCES customers(organization_id, uid)
)
-- rollback DROP TABLE linebot_user_map;

-- changeset beans:KW-2421:********:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('line_channel_id')) and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs ADD line_channel_id varchar(16);
ALTER TABLE oauth_client_configs ADD FOREIGN KEY (line_channel_id) REFERENCES studio_linebot_config(channel_id);
-- rollback ALTER TABLE oauth_client_configs DROP FOREIGN KEY line_channel_id, DROP COLUMN line_channel_id;

-- changeset beans:KW-2421:********:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('compliance_organization_id')) and TABLE_NAME = 'studio_organizations' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organizations ADD compliance_organization_id int;
-- rollback ALTER TABLE studio_organizations DROP COLUMN compliance_organization_id;

-- changeset raiven:member-module:20231121:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'admin') and TABLE_NAME = 'studio_organization_modules' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
UPDATE studio_organization_modules SET admin = admin & ~1 where admin & 1;
UPDATE studio_organization_modules SET admin = admin & ~2 where admin & 2;
ALTER TABLE studio_organization_modules MODIFY COLUMN admin set('billing') DEFAULT NULL;
-- rollback ALTER TABLE studio_organization_modules MODIFY COLUMN admin set('members','settings','billing') DEFAULT NULL;

-- changeset raiven:otc-asset-pro-operators:20231127:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM wallet.studio_organization_modules WHERE organization_id = 1;
UPDATE `wallet`.`studio_organization_modules` SET asset_pro = asset_pro | 8 where organization_id = 1;
-- rollback UPDATE `wallet`.`studio_organization_modules` SET asset_pro = asset_pro & ~8 where organization_id = 1;

-- changeset beans:KW-2492:20231123:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('application_type')) and TABLE_NAME = 'studio_organization_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_clients ADD application_type ENUM('linebot', 'complyflow', 'mobile_wallet', 'web_app');
-- rollback ALTER TABLE studio_organization_clients DROP COLUMN application_type;

-- changeset beans:KW-2494:20231124:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'studio_exchange_rate' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE IF NOT EXISTS `studio_exchange_rate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `organization_id` int NOT NULL,
  `token_base` varchar(16) NOT NULL,
  `token_quote` varchar(60) NOT NULL,
  `buy_price` decimal(24, 12),
  `sell_price` decimal(24, 12),
  `image_url` varchar(255),
  `description` varchar(255),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_org_token` (`organization_id`, `token_base`, `token_quote`),
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id)
)
-- rollback DROP TABLE studio_exchange_rate;

-- changeset jason:KW-2586:20231129:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'secret' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs ADD secret varchar(50) NOT NULL;
-- rollback ALTER TABLE oauth_client_configs DROP COLUMN secret;

-- changeset jason:KW-2586:20231129:2 context:local
UPDATE oauth_client_configs SET secret = 'F2lfScH7r4c...' WHERE name = 'Stickey';
UPDATE oauth_client_configs SET secret = 'IASEBX4mNTQKEDvW33iAq0UotQAKPbgV' WHERE name = 'Tag';
UPDATE oauth_client_configs SET secret = 'vgWdVMPILGukPcdhqr547XKmpy2iRGN5' WHERE name = 'KryptoGO';
-- rollback UPDATE oauth_client_configs SET secret = '' WHERE name in ('Stickey', 'Tag', 'KryptoGO');

-- changeset jason:KW-2586:20231129:2 context:dev
UPDATE oauth_client_configs SET secret = 'Ii7IH00KO8X7n3NBZvpGMKoDJiicyh4t' WHERE name = 'Stickey';
UPDATE oauth_client_configs SET secret = 'nTHvJaLUWtfXhdBPw09jXUK3JtQXUgYq' WHERE name = 'Tag';
UPDATE oauth_client_configs SET secret = 'Maw89T9vok3uhSdJictUDKODZ5jUjtI9' WHERE name = 'KryptoGO';
-- rollback UPDATE oauth_client_configs SET secret = '' WHERE name in ('Stickey', 'Tag', 'KryptoGO');

-- changeset jason:KW-2586:20231129:2 context:staging
UPDATE oauth_client_configs SET secret = 'ZdYMkoULvTvJ0GGATx7YmOM15GPdbmac' WHERE name = 'Stickey';
UPDATE oauth_client_configs SET secret = 'VPZyajD7q937XUKPwDDTrEchS20XmFTD' WHERE name = 'Tag';
UPDATE oauth_client_configs SET secret = 'WywfYmYiJ84PeCNSW8hINIrDOacb3AkK' WHERE name = 'KryptoGO';
-- rollback UPDATE oauth_client_configs SET secret = '' WHERE name in ('Stickey', 'Tag', 'KryptoGO');

-- changeset jason:KW-2586:20231129:2 context:prod
UPDATE oauth_client_configs SET secret = 'DQlY5Mf3qnXrXQ9NFM5EBtvO18dbu3nY' WHERE name = 'Stickey';
UPDATE oauth_client_configs SET secret = 'XyNL5MtHWMHj2EWmzGz55LlrDSGGYkh2' WHERE name = 'Tag';
UPDATE oauth_client_configs SET secret = 'kz71uyF58ThmPmd1Cakm6iniWGL999rZ' WHERE name = 'KryptoGO';
-- rollback UPDATE oauth_client_configs SET secret = '' WHERE name in ('Stickey', 'Tag', 'KryptoGO');

-- changeset alan:fix-dapp:20231212:1
CREATE TABLE IF NOT EXISTS `studio_organization_dapps` (
  `id` int NOT NULL AUTO_INCREMENT,
  `organization_id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `site_url` varchar(255) NOT NULL,
  `is_active` boolean NOT NULL,
  PRIMARY KEY (`id`),
  INDEX idx_organization_id (organization_id)
)
-- rollback DROP TABLE studio_organization_dapps;

-- changeset alan:KW-2791:20231222:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('average_price','one_day_average_price','seven_day_average_price','thirty_day_average_price')) and TABLE_NAME = 'nft_collections' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE nft_collections ADD average_price decimal(18, 6) DEFAULT 0;
ALTER TABLE nft_collections ADD one_day_average_price decimal(18, 6) DEFAULT 0;
ALTER TABLE nft_collections ADD seven_day_average_price decimal(18, 6) DEFAULT 0;
ALTER TABLE nft_collections ADD thirty_day_average_price decimal(18, 6) DEFAULT 0;
-- rollback ALTER TABLE nft_collections DROP COLUMN average_price,DROP COLUMN one_day_average_price,DROP COLUMN seven_day_average_price, DROP COLUMN thirty_day_average_price;

-- changeset harry:fix-image-url-too-long:20240109:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'image_preview_url' AND DATA_TYPE = 'varchar' AND CHARACTER_MAXIMUM_LENGTH = 255 AND TABLE_NAME = 'nft_assets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `nft_assets` MODIFY COLUMN `image_url` text NOT NULL;
ALTER TABLE `nft_assets` MODIFY COLUMN `image_preview_url` text NOT NULL;
ALTER TABLE `nft_assets` MODIFY COLUMN `collection_image_url` text;
ALTER TABLE `nft_assets` MODIFY COLUMN `animation_url` text;
ALTER TABLE `dashboard_projects` MODIFY COLUMN `image_url` text NOT NULL;
ALTER TABLE `dashboard_projects` MODIFY COLUMN `large_image_url` text NOT NULL;
ALTER TABLE `dashboard_prizes` MODIFY COLUMN `image_url` text;
ALTER TABLE `dashboard_prizes` MODIFY COLUMN `redeem_url` text;
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `image_url` text;
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `external_url` text;
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `animation_url` text;
ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `image_url` text NOT NULL;
ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `site_url` text NOT NULL;
ALTER TABLE `studio_exchange_rate` MODIFY COLUMN `image_url` text;
ALTER TABLE `notifications` MODIFY COLUMN `preview_image_url` text NOT NULL;
-- rollback ALTER TABLE `nft_assets` MODIFY COLUMN `image_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `nft_assets` MODIFY COLUMN `image_preview_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `nft_assets` MODIFY COLUMN `collection_image_url` varchar(250);
-- rollback ALTER TABLE `nft_assets` MODIFY COLUMN `animation_url` varchar(255);
-- rollback ALTER TABLE `dashboard_projects` MODIFY COLUMN `image_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `dashboard_projects` MODIFY COLUMN `large_image_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `dashboard_prizes` MODIFY COLUMN `image_url` varchar(255);
-- rollback ALTER TABLE `dashboard_prizes` MODIFY COLUMN `redeem_url` varchar(250);
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `image_url` varchar(255);
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `external_url` varchar(255);
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `animation_url` varchar(255);
-- rollback ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `image_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `site_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `studio_exchange_rate` MODIFY COLUMN `image_url` varchar(255);
-- rollback ALTER TABLE `notifications` MODIFY COLUMN `preview_image_url` varchar(255) NOT NULL;

-- changeset jason:KW-2731:20240110:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'contacts' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE IF NOT EXISTS `contacts` (
  `uid` varchar(36) NOT NULL PRIMARY KEY,
  `owner_id` varchar(60) NOT NULL,
  `user_id` varchar(60),
  `phone_number` varchar(255),
  `nickname` varchar(255),
  `name` varchar(255),
  `btc` varchar(255),
  `eth` varchar(255),
  `bsc` varchar(255),
  `matic` varchar(255),
  `tron` varchar(255),
  `sol` varchar(255),
  `kcc` varchar(255),
  `arb` varchar(255),
  `ronin` varchar(255),
  `oasys` varchar(255),
  `is_manual_address` tinyint(1),
  `is_added_by_qr` tinyint(1),
  `is_favorite` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_contacts_owner_id` (`owner_id`),
  UNIQUE INDEX `idx_contacts_owner_id_user_id` (`owner_id`, `user_id`),
  UNIQUE INDEX `idx_contacts_owner_id_phone_number` (`owner_id`, `phone_number`)
)
-- rollback DROP TABLE contacts;

-- changeset alan:KW-2934:20240112:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('photo_url', 'country', 'national_id', 'email', 'phone', 'line_id', 'gender', 'physical_address', 'legal_name', 'kyc_status', 'idv_status', 'cdd_risk_score', 'sanction_matched')) and TABLE_NAME = 'customers' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE customers
  ADD COLUMN photo_url TEXT,
  ADD COLUMN country VARCHAR(64),
  ADD COLUMN national_id VARCHAR(64),
  ADD COLUMN birthday DATETIME,
  ADD COLUMN passport_number VARCHAR(14),
  ADD COLUMN email VARCHAR(100),
  ADD COLUMN phone VARCHAR(20),
  ADD COLUMN line_id VARCHAR(45),
  ADD COLUMN gender ENUM('male', 'female'),
  ADD COLUMN physical_address TEXT,
  ADD COLUMN legal_name VARCHAR(256),
  ADD COLUMN kyc_status ENUM('rejected', 'verified', 'unverified', 'pending') NOT NULL DEFAULT 'unverified',
  ADD COLUMN idv_status ENUM('pending', 'reject', 'accept', 'review', 'initial'),
  ADD COLUMN cdd_risk_score TINYINT UNSIGNED,
  ADD COLUMN sanction_matched TINYINT(1);
CREATE INDEX idx_kyc_status ON customers (kyc_status);
-- rollback ALTER TABLE customers DROP COLUMN photo_url, DROP COLUMN country, DROP COLUMN national_id, DROP COLUMN birthday, DROP COLUMN passport_number, DROP COLUMN email, DROP COLUMN phone, DROP COLUMN line_id, DROP COLUMN gender, DROP COLUMN physical_address, DROP COLUMN legal_name, DROP COLUMN kyc_status, DROP COLUMN idv_status, DROP COLUMN cdd_risk_score, DROP COLUMN sanction_matched;

-- changeset alan:KW-2936:20240122:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME ='kyc_status' AND COLUMN_TYPE = "enum('rejected','verified','unverified','pending')" and TABLE_NAME = 'customers' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE customers MODIFY COLUMN kyc_status ENUM('rejected','verified','unverified','pending','processing') NOT NULL DEFAULT 'unverified';
-- rollback ALTER TABLE customers MODIFY COLUMN kyc_status ENUM('rejected','verified','unverified','pending') NOT NULL DEFAULT 'unverified';

-- changeset alan:KW-2936:20240122:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME ='birthday' AND COLUMN_TYPE = 'datetime' and TABLE_NAME = 'customers' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE customers MODIFY COLUMN birthday VARCHAR(10);
-- rollback ALTER TABLE customers MODIFY COLUMN birthday DATETIME;

-- changeset alan:KW-2935:20240115:1
CREATE TABLE IF NOT EXISTS `case_submissions` (
 		`id` int NOT NULL AUTO_INCREMENT,
    `organization_id` int NOT NULL,
    `uid` VARCHAR(60) NOT NULL,
    `submitted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		`form_id` integer,
		`idv_id` integer,
		`cdd_id` integer,
	  PRIMARY KEY (`id`),
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
		INDEX idx_case_submission (organization_id, uid),
    INDEX idx_case_submission_submitted_at (submitted_at)
)
-- rollback DROP TABLE case_submission;

-- changeset alan:fix-nft-boost:20240122:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'favicon_url' and TABLE_NAME = 'airdrop_events' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE airdrop_events ADD favicon_url TEXT;
-- rollback ALTER TABLE airdrop_events DROP COLUMN favicon_url;

-- changeset jason:KW-2969:20240118:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'kyc_audit_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE IF NOT EXISTS `kyc_audit_logs` (
		id int NOT NULL AUTO_INCREMENT PRIMARY KEY,
    organization_id INT NOT NULL,
    uid VARCHAR(60) NOT NULL,
		auditor_uid varchar(60) NOT NULL,
		before_status ENUM('rejected','verified','unverified','pending','processing') NOT NULL,
		after_status ENUM('rejected','verified','unverified','pending','processing') NOT NULL,
		reject_reasons set('sanctioned', 'high_risk_money_laundering', 'faked_document', 'not_the_document_holder', 'incorrect_document_information'),
    internal_notes text,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id),
    INDEX idx_organization_id (organization_id),
    INDEX idx_organization_id_uid (organization_id, uid)
);
-- rollback DROP TABLE kyc_audit_logs;

-- changeset jason:KW-2969:20240118:2
--preconditions onFail:CONTINUE
-- add compliance:reviewer to studio_roles, and apply to all existing studio_role_bindings
--precondition-sql-check expectedResult:0 SELECT count(*) FROM studio_roles WHERE module = 'compliance' AND name = 'reviewer';
INSERT INTO `studio_roles` (`module`, `name`) VALUES ('compliance', 'reviewer');
-- rollback DELETE FROM studio_roles WHERE module = 'compliance' AND name = 'reviewer';

-- changeset alan:fix-nft-boost:20240207:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'organization_id' and TABLE_NAME = 'studio_nft_projects' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_nft_projects ADD organization_id INT;
-- rollback ALTER TABLE studio_nft_projects DROP COLUMN organization_id;

-- changeset alan:fix-nft-boost:20240207:2
UPDATE studio_nft_projects JOIN studio_organizations ON studio_nft_projects.organization = studio_organizations.name SET studio_nft_projects.organization_id = studio_organizations.id WHERE studio_nft_projects.organization_id IS NULL AND studio_nft_projects.project_id>0;
-- rollback ;

-- changeset alan:fix-case-submission:20240229:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = 'case_submissions' AND index_name='idx_form_id_case_submission' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE case_submissions ADD CONSTRAINT idx_form_id_case_submission UNIQUE (form_id);
-- rollback ALTER TABLE case_submissions DROP CONSTRAINT idx_form_id_case_submission;


-- changeset alan:fix-airdrop-signing-v2:20240304:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME = 'studio_nft_projects' AND index_name='idx_studio_nft_projects_collection_name' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_nft_projects ADD CONSTRAINT idx_studio_nft_projects_collection_name UNIQUE (collection_name);
-- rollback ALTER TABLE studio_nft_projects DROP CONSTRAINT idx_studio_nft_projects_collection_name;

-- changeset alan:fix-airdrop-signing-v2:20240304:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'collection_name' AND IS_NULLABLE = 'YES' AND TABLE_NAME = 'studio_nft_projects' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_nft_projects MODIFY COLUMN collection_name VARCHAR(250) NOT NULL;
-- rollback ALTER TABLE studio_nft_projects MODIFY COLUMN collection_name VARCHAR(250);

-- changeset raiven:KW-3128:20240308:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('wide_logo', 'scopes') AND TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs ADD COLUMN wide_logo TEXT NULL;
ALTER TABLE oauth_client_configs ADD COLUMN scopes JSON;
-- rollback ALTER TABLE oauth_client_configs DROP COLUMN wide_logo, scopes;

-- changeset raiven:KW-3171:20240312:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'asset_pro') and TABLE_NAME = 'studio_organization_modules' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_modules MODIFY COLUMN asset_pro set('treasury','send_token','transaction_history','operators','market') DEFAULT NULL;
-- rollback ALTER TABLE studio_organization_modules MODIFY COLUMN asset_pro set('treasury','send_token','transaction_history','operators') DEFAULT NULL;

-- changeset raiven:KW-3171:20240312:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'application_type') and TABLE_NAME = 'studio_organization_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_clients MODIFY COLUMN application_type set('linebot','complyflow','mobile_wallet','web_app','market') DEFAULT NULL;
-- rollback ALTER TABLE studio_organization_clients MODIFY COLUMN application_type set('linebot','complyflow','mobile_wallet','web_app') DEFAULT NULL;

-- changeset raiven:KW-3171:20240312:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'studio_markets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE IF NOT EXISTS `studio_markets` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `organization_id` INT NOT NULL,
  `market_url` VARCHAR(255) NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `logo` TEXT NOT NULL,
  `email` varchar(320),
  `phone` varchar(15),
  `line_id` varchar(20),
  `introduction` TEXT,
  `payment_method` ENUM('bank-transfer') NOT NULL,
  `payment_currency` VARCHAR(3) NOT NULL,
  `bank_name` VARCHAR(255),
  `branch_name` VARCHAR(255),
  `bank_account` VARCHAR(255),
  `account_holder_name` VARCHAR(255),
  `payment_expiration_sec` INT NOT NULL DEFAULT 86400,
  FOREIGN KEY (`organization_id`) REFERENCES `studio_organizations` (`id`),
  UNIQUE INDEX uniq_market_url (market_url),
  UNIQUE INDEX uniq_studio_org_id_studio_market (organization_id)
);
-- rollback DROP TABLE studio_markets;

-- changeset alan:KW-3164:********:1
--preconditions onFail:CONTINUE
CREATE TABLE IF NOT EXISTS `asset_pro_product_base_infos` (
 		`id` int NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(30) NOT NULL,
		`chain_id` VARCHAR(20) NOT NULL,
    `image` TEXT NOT NULL,
		`base_currency` ENUM('USDT', 'USDC') NOT NULL,
		`quote_currency` ENUM('TWD') NOT NULL,
    `type` ENUM ('buy_crypto') NOT NULL,
    `token_logo` TEXT NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		`deleted_at` timestamp,
		PRIMARY KEY (id)
);
-- rollback drop table asset_pro_product_base_infos;

-- changeset alan:KW-3164:********:2
--preconditions onFail:CONTINUE
CREATE TABLE IF NOT EXISTS `asset_pro_products` (
 		`id` int NOT NULL AUTO_INCREMENT,
    `organization_id` int NOT NULL,
    `product_base_info_id` int NOT NULL,
    `is_published` BOOLEAN NOT NULL DEFAULT false,
    `image` TEXT,
    `name` VARCHAR(30) NOT NULL,
    `price` DECIMAL(18,6),
    `order_limits_from` DECIMAL(18,6),
    `order_limits_to` DECIMAL(18,6),
    `stock` DECIMAL(18,6),
    `fee_type` ENUM('no_fee', 'fee_included') NOT NULL,
		`proportional_fee_percentage` DECIMAL(6,4),
		`minimum_proportional_fee` DECIMAL(8,2) COMMENT 'set fee as it when proportional_fee * total is less than this value',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		`deleted_at` timestamp,
		PRIMARY KEY (id),
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id),
    FOREIGN KEY (product_base_info_id) REFERENCES asset_pro_product_base_infos(id),
		INDEX idx_asset_pro_products_organization_id (organization_id)
);
-- rollback drop table asset_pro_products;

-- changeset alan:KW-3164:********:3 context:local
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos;
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image) VALUES
('USDT/ETH', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_eth.png'),
('USDC/ETH', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_eth.png'),
('USDT/MATIC', 'matic', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_matic.png'),
('USDC/MATIC', 'matic', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_matic.png'),
('USDT/TRON', 'tron', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_tron.png'),
('USDC/TRON', 'tron', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_tron.png'),
('USDT/BNB', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_bnb.png'),
('USDC/BNB', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_bnb.png'),
('USDT/SEPOLIA', 'sepolia', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_matic.png'),
('USDC/SEPOLIA', 'sepolia', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_matic.png'),
('USDT/SHASTA', 'shasta', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_tron.png'),
('USDC/SHASTA', 'shasta', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_tron.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('USDT/ETH', 'USDC/ETH', 'USDT/MATIC', 'USDC/MATIC', 'USDT/TRON', 'USDC/TRON', 'USDT/BNB', 'USDC/BNB', 'USDT/SEPOLIA', 'USDC/SEPOLIA', 'USDT/SHASTA', 'USDC/SHASTA');


-- changeset alan:KW-3164:********:3 context:dev
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos;
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image) VALUES
('USDT/ETH', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_eth.png'),
('USDC/ETH', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_eth.png'),
('USDT/MATIC', 'matic', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_matic.png'),
('USDC/MATIC', 'matic', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_matic.png'),
('USDT/TRON', 'tron', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_tron.png'),
('USDC/TRON', 'tron', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_tron.png'),
('USDT/BNB', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_bnb.png'),
('USDC/BNB', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_bnb.png'),
('USDT/SEPOLIA', 'sepolia', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_matic.png'),
('USDC/SEPOLIA', 'sepolia', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_matic.png'),
('USDT/SHASTA', 'shasta', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_tron.png'),
('USDC/SHASTA', 'shasta', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_tron.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('USDT/ETH', 'USDC/ETH', 'USDT/MATIC', 'USDC/MATIC', 'USDT/TRON', 'USDC/TRON', 'USDT/BNB', 'USDC/BNB', 'USDT/SEPOLIA', 'USDC/SEPOLIA', 'USDT/SHASTA', 'USDC/SHASTA');

-- changeset alan:KW-3164:********:3 context:staging
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos;
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image) VALUES
('USDT/ETH', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_eth.png'),
('USDC/ETH', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_eth.png'),
('USDT/MATIC', 'matic', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_matic.png'),
('USDC/MATIC', 'matic', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_matic.png'),
('USDT/TRON', 'tron', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png'),
('USDC/TRON', 'tron', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_tron.png'),
('USDT/BNB', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_bnb.png'),
('USDC/BNB', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_bnb.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('USDT/ETH', 'USDC/ETH', 'USDT/MATIC', 'USDC/MATIC', 'USDT/TRON', 'USDC/TRON', 'USDT/BNB', 'USDC/BNB');


-- changeset alan:KW-3164:********:3 context:prod
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos;
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image) VALUES
('USDT/ETH', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_eth.png'),
('USDC/ETH', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_eth.png'),
('USDT/MATIC', 'matic', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_matic.png'),
('USDC/MATIC', 'matic', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_matic.png'),
('USDT/TRON', 'tron', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png'),
('USDC/TRON', 'tron', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_tron.png'),
('USDT/BNB', 'eth', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_bnb.png'),
('USDC/BNB', 'eth', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_bnb.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('USDT/ETH', 'USDC/ETH', 'USDT/MATIC', 'USDC/MATIC', 'USDT/TRON', 'USDC/TRON', 'USDT/BNB', 'USDC/BNB');

-- changeset alan:asset-pro-orders:20240314:1
CREATE TABLE IF NOT EXISTS asset_pro_orders (
    `id` VARCHAR(56) NOT NULL,
    `organization_id` INT NOT NULL,
    `customer_uid` VARCHAR(60) NOT NULL,
    `operator_uid` VARCHAR(60),
    `wallet_address` VARCHAR(62) NOT NULL,
    `product_id` INT NOT NULL,
    `amount` DECIMAL(18,6) NOT NULL,
    `usd_amount` DECIMAL(18,6) NOT NULL,
    `total_cost` DECIMAL(25,6) NOT NULL,
    `usd_total_cost` DECIMAL(25,6) NOT NULL,
    `price` DECIMAL(18,6) NOT NULL,
    `exchange_rate` DECIMAL(10,4) NOT NULL,
	  `fee_type` ENUM('no_fee', 'additional_fee') NOT NULL,
    `proportional_fee_percentage` DECIMAL(6,4) NULL,
    `proportional_minimum_fee` DECIMAL(25,6) NULL,
    `tx_id` INT,
    `payment_status` ENUM('paid', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL,
    `order_status` ENUM('unpaid', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled') NOT NULL,
    `deadline` TIMESTAMP NOT NULL,
    `transfer_to_bank_name` VARCHAR(25),
    `transfer_to_branch_name` VARCHAR(25),
    `transfer_to_account_number` VARCHAR(20),
    `transfer_to_account_holder_name` VARCHAR(20),
    `payment_note` TEXT,
    `payment_attachments` JSON,
    `last_five_digits` VARCHAR(5),
    `customer_attachments` JSON,
    `internal_note` TEXT,
    `customer_updated_at` TIMESTAMP,
    `operator_updated_at` TIMESTAMP,
    `cancelled_at` TIMESTAMP,
    `refunded_at` TIMESTAMP,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`organization_id`) REFERENCES studio_organizations(`id`),
	  FOREIGN KEY (`organization_id`, `customer_uid`) REFERENCES customers(`organization_id`, `uid`),
    FOREIGN KEY (`product_id`) REFERENCES asset_pro_products(`id`),
    FOREIGN KEY (`tx_id`) REFERENCES assetpro_tx_logs(`id`),
    INDEX `idx_asset_pro_orders_organization_id` (`organization_id`),
    INDEX `idx_asset_pro_orders_uid__organization_id` (`customer_uid`, `organization_id`)
);
-- rollback DROP TABLE asset_pro_orders;

-- changeset alan:asset-pro-orders:20240314:2
CREATE TABLE IF NOT EXISTS asset_pro_order_audit_logs (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `order_id` VARCHAR(56) NOT NULL,
    `organization_id` INT NOT NULL,
    `customer_uid` VARCHAR(60),
    `operator_uid` VARCHAR(60),
    `payment_status_old` ENUM('paid', 'unpaid', 'awaiting_refund', 'refunded'),
    `payment_status_new` ENUM('paid', 'unpaid', 'awaiting_refund', 'refunded'),
    `order_status_old` ENUM('unpaid', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled'),
    `order_status_new` ENUM('unpaid', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled'),
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES asset_pro_orders(id)
);
-- rollback DROP TABLE asset_pro_order_audit_logs;

-- changeset jason:KW-3219:********:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('login_methods') AND TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs ADD COLUMN login_methods JSON;
-- rollback ALTER TABLE oauth_client_configs DROP COLUMN login_methods;


-- changeset alan:fix-asset-pro-orders:********:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'minimum_proportional_fee' and TABLE_NAME = 'asset_pro_products' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products CHANGE COLUMN minimum_proportional_fee proportional_minimum_fee DECIMAL(25,6) NULL COMMENT 'set fee as it when proportional_fee * total is less than this value';
-- rollback ALTER TABLE asset_pro_products CHANGE COLUMN proportional_minimum_fee minimum_proportional_fee DECIMAL(8,2) COMMENT 'set fee as it when proportional_fee * total is less than this value';

-- changeset alan:fix-asset-pro-orders:********:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'quote_currency_logo' and TABLE_NAME = 'asset_pro_product_base_infos' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_product_base_infos ADD COLUMN quote_currency_logo TEXT;
UPDATE asset_pro_product_base_infos SET quote_currency_logo = 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png' WHERE quote_currency = 'TWD';
-- rollback ALTER TABLE asset_pro_product_base_infos DROP COLUMN quote_currency_logo;

-- changeset alan:fix-asset-pro-orders:********:3
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'fee_type' and TABLE_NAME = 'asset_pro_orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_orders MODIFY COLUMN fee_type ENUM('no_fee', 'additional_fee', 'fee_included') NOT NULL;
UPDATE asset_pro_orders SET fee_type = 'fee_included' WHERE fee_type = 'additional_fee' and id >"";
ALTER TABLE asset_pro_orders MODIFY COLUMN fee_type ENUM('no_fee', 'fee_included') NOT NULL;
-- rollback ;

-- changeset alan:fix-asset-pro-orders:********:4
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'payment_method' and TABLE_NAME = 'asset_pro_orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_orders ADD COLUMN payment_method ENUM('bank_transfer') NOT NULL DEFAULT 'bank_transfer';
-- rollback ALTER TABLE asset_pro_orders DROP COLUMN payment_method;

-- changeset alan:fix-asset-pro-orders:********:5
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'payment_method' and TABLE_NAME = 'studio_markets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_markets MODIFY COLUMN payment_method ENUM('bank-transfer','bank_transfer') NOT NULL DEFAULT 'bank_transfer';
UPDATE studio_markets SET payment_method = 'bank_transfer' WHERE payment_method = 'bank-transfer' and id >0;
ALTER TABLE studio_markets MODIFY COLUMN payment_method ENUM('bank_transfer') NOT NULL DEFAULT 'bank_transfer';
-- rollback ;

-- changeset raiven:add-fk-studio-organization-clients:********
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'client_id' and TABLE_NAME = 'studio_organization_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'id' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_clients
ADD CONSTRAINT fk_studio_organization_clients_client_id FOREIGN KEY (client_id) REFERENCES oauth_client_configs(id);
-- rollback ALTER TABLE studio_organization_clients DROP FOREIGN KEY fk_studio_organization_clients_client_id;

-- changeset alan:KW-3164:********:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'operator_uid' and TABLE_NAME = 'asset_pro_products' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD COLUMN operator_uid VARCHAR(60);
-- rollback ALTER TABLE asset_pro_products DROP COLUMN operator_uid;

-- changeset alan:KW-3164:********:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'fk_asset_pro_products_operator' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'FOREIGN KEY' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT fk_asset_pro_products_operator FOREIGN KEY (organization_id, operator_uid) REFERENCES studio_users (organization_id, uid);
-- rollback ALTER TABLE asset_pro_products DROP FOREIGN KEY fk_asset_pro_products_operator;

-- changeset alan:KW-3164:********:3
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'check_price' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'CHECK' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT check_price CHECK (price >= 0);
-- rollback ALTER TABLE asset_pro_products DROP CONSTRAINT check_price;

-- changeset alan:KW-3164:********:4
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'check_order_limits_from' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'CHECK' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT check_order_limits_from CHECK (order_limits_from >= 0);
-- rollback ALTER TABLE asset_pro_products DROP CONSTRAINT check_order_limits_from;

-- changeset alan:KW-3164:********:5
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'check_order_limits_to' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'CHECK' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT check_order_limits_to CHECK (order_limits_to >= 0 AND order_limits_from <= order_limits_to);
-- rollback ALTER TABLE asset_pro_products DROP CONSTRAINT check_order_limits_to;

-- changeset alan:KW-3164:********:6
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'check_stock' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'CHECK' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT check_stock CHECK (stock >= 0);
-- rollback ALTER TABLE asset_pro_products DROP CONSTRAINT check_stock;

-- changeset alan:KW-3164:********:7
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'check_proportional_fee_percentage' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'CHECK' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT check_proportional_fee_percentage CHECK (proportional_fee_percentage >= 0 AND proportional_fee_percentage <= 100);
-- rollback ALTER TABLE asset_pro_products DROP CONSTRAINT check_proportional_fee_percentage;

-- changeset alan:KW-3164:********:8
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME = 'check_proportional_minimum_fee' AND TABLE_NAME = 'asset_pro_products' AND CONSTRAINT_TYPE = 'CHECK' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_products ADD CONSTRAINT check_proportional_minimum_fee CHECK (proportional_minimum_fee >= 0);
-- rollback ALTER TABLE asset_pro_products DROP CONSTRAINT check_proportional_minimum_fee;

-- changeset harry:fix-gorm-model:20240326:1
ALTER TABLE studio_organization_clients MODIFY COLUMN application_type ENUM('linebot','complyflow','mobile_wallet','web_app','market');
-- rollback ALTER TABLE studio_organization_clients MODIFY COLUMN application_type set('linebot','complyflow','mobile_wallet','web_app') DEFAULT NULL;

-- changeset harry:KW-3243:20240326:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'is_custom_auth' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'verifier_type' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `oauth_client_configs` ADD `is_custom_auth` tinyint(1) NOT NULL DEFAULT false;
ALTER TABLE `oauth_client_configs` ADD `verifier_type` enum('google', 'auth0', 'jwt', 'custom_twm');
-- rollback ALTER TABLE `oauth_client_configs` DROP `is_custom_auth`, DROP `verifier_type`;

-- changeset harry:KW-3266:20240331:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'jwk_url' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'jwt_audience' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'jwt_issuer' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'jwt_uid_field' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `oauth_client_configs` ADD `jwk_url` varchar(255);
ALTER TABLE `oauth_client_configs` ADD `jwt_audience` varchar(255);
ALTER TABLE `oauth_client_configs` ADD `jwt_issuer` varchar(255);
ALTER TABLE `oauth_client_configs` ADD `jwt_uid_field` varchar(50);
-- rollback ALTER TABLE `oauth_client_configs` DROP `jwk_url`, DROP `jwt_audience`, DROP `jwt_issuer`, DROP `jwt_uid_field`;

-- changeset harry:KW-3266:20240401:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_NAME='uniq_studio_organization_clients' and TABLE_NAME = 'studio_organization_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_clients DROP CONSTRAINT uniq_studio_organization_clients;
-- rollback ALTER TABLE studio_organization_clients ADD CONSTRAINT uniq_studio_organization_clients UNIQUE (organization_id, client_id);

-- changeset harry:KW-3266:20240402:1 dropping `secret_env_var` VARCHAR(50) NOT NULL in `oauth_client_configs` table
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'secret_env_var' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs DROP COLUMN secret_env_var;
-- rollback ALTER TABLE oauth_client_configs ADD COLUMN secret_env_var VARCHAR(50) NOT NULL;


-- changeset alan:KW-3265:20240328:1
--preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'client_id' and TABLE_NAME = 'studio_markets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'UNIQUE' AND TABLE_NAME = 'studio_markets' AND CONSTRAINT_NAME = 'uniq_studio_markets_clients' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_markets ADD COLUMN client_id VARCHAR(50) NOT NULL;
ALTER TABLE studio_markets ADD UNIQUE INDEX uniq_studio_markets_clients (client_id);
-- rollback ALTER TABLE studio_markets DROP COLUMN client_id; ALTER TABLE studio_markets DROP INDEX uniq_studio_markets_clients;

-- changeset alan:KW-3265:20240328:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'market_code' and TABLE_NAME = 'studio_markets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'UNIQUE' AND TABLE_NAME = 'studio_markets' AND CONSTRAINT_NAME = 'uniq_studio_markets_codes' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_markets ADD COLUMN market_code VARCHAR(50) NOT NULL;
ALTER TABLE studio_markets ADD UNIQUE INDEX uniq_studio_markets_codes (market_code);
-- rollback ALTER TABLE studio_markets DROP COLUMN market_code; ALTER TABLE studio_markets DROP INDEX uniq_studio_markets_codes;

-- changeset raiven:KW-3276:********:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('bank_name', 'branch_name', 'account_number', 'account_holder_name') and TABLE_NAME = 'customers' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `customers` ADD `bank_name` VARCHAR(255);
ALTER TABLE `customers` ADD `branch_name` VARCHAR(255);
ALTER TABLE `customers` ADD `account_number` VARCHAR(255);
ALTER TABLE `customers` ADD `account_holder_name` VARCHAR(255);
-- rollback ALTER TABLE `customers` DROP `bank_name`, DROP `branch_name`, DROP `account_number`, DROP `account_holder_name`;

-- changeset raiven:add_awaiting_confirm:********:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'payment_status' and TABLE_NAME = 'asset_pro_orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
-- precondition-sql-check expectedResult:2 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('payment_status_old', 'payment_status_new') and TABLE_NAME = 'asset_pro_order_audit_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_orders MODIFY COLUMN payment_status ENUM('unpaid', 'awaiting_confirm', 'paid', 'awaiting_refund', 'refunded') NOT NULL;
ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_old ENUM('unpaid', 'awaiting_confirm', 'paid', 'awaiting_refund', 'refunded');
ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_new ENUM('unpaid', 'awaiting_confirm', 'paid', 'awaiting_refund', 'refunded');
-- rollback UPDATE asset_pro_orders SET payment_status = 'paid' WHERE payment_status = 'awaiting_confirm';
-- rollback UPDATE asset_pro_order_audit_logs SET payment_status_old = 'paid' WHERE payment_status_old = 'awaiting_confirm';
-- rollback UPDATE asset_pro_order_audit_logs SET payment_status_new = 'paid' WHERE payment_status_new = 'awaiting_confirm';
-- rollback ALTER TABLE asset_pro_orders MODIFY COLUMN payment_status ENUM('paid', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL;
-- rollback ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_old ENUM('paid', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL;
-- rollback ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_new ENUM('paid', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL;

-- changeset raiven:add_awaiting_confirm:********:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'payment_status' and TABLE_NAME = 'asset_pro_orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
-- precondition-sql-check expectedResult:2 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('payment_status_old', 'payment_status_new') and TABLE_NAME = 'asset_pro_order_audit_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_orders MODIFY COLUMN payment_status ENUM('unpaid', 'paid', 'awaiting_refund', 'refunded') NOT NULL;
ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_old ENUM('unpaid', 'paid', 'awaiting_refund', 'refunded');
ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_new ENUM('unpaid', 'paid', 'awaiting_refund', 'refunded');
-- rollback ALTER TABLE asset_pro_orders MODIFY COLUMN payment_status ENUM('paid', 'awaiting_confirm', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL;
-- rollback ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_old ENUM('paid', 'awaiting_confirm', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL;
-- rollback ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN payment_status_new ENUM('paid', 'awaiting_confirm', 'unpaid', 'awaiting_refund', 'refunded') NOT NULL;

-- changeset raiven:add_awaiting_confirm:********:3
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'order_status' and TABLE_NAME = 'asset_pro_orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
-- precondition-sql-check expectedResult:2 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('order_status_old', 'order_status_new') and TABLE_NAME = 'asset_pro_order_audit_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_orders MODIFY COLUMN order_status ENUM('unpaid', 'awaiting_confirmation', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled') NOT NULL;
ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN order_status_old ENUM('unpaid', 'awaiting_confirmation', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled');
ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN order_status_new ENUM('unpaid', 'awaiting_confirmation', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled');
-- rollback UPDATE asset_pro_orders SET order_status = 'unpaid' WHERE order_status = 'awaiting_confirmation';
-- rollback UPDATE asset_pro_order_audit_logs SET order_status_old = 'unpaid' WHERE order_status_old = 'awaiting_confirmation';
-- rollback UPDATE asset_pro_order_audit_logs SET order_status_new = 'unpaid' WHERE order_status_new = 'awaiting_confirmation';
-- rollback ALTER TABLE asset_pro_orders MODIFY COLUMN order_status ENUM('unpaid', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled') NOT NULL;
-- rollback ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN order_status_old ENUM('unpaid', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled') NOT NULL;
-- rollback ALTER TABLE asset_pro_order_audit_logs MODIFY COLUMN order_status_new ENUM('unpaid', 'awaiting_shipment', 'shipping', 'delivered', 'cancelled') NOT NULL;

-- changeset raiven:add_payment_amount:********:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'payment_amount' and TABLE_NAME = 'asset_pro_orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE asset_pro_orders ADD COLUMN payment_amount DECIMAL(25,6);
-- rollback ALTER TABLE asset_pro_orders DROP COLUMN payment_amount;

-- changeset alan:fix-txlist:20240419:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:4 SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'tx_lists' AND CONSTRAINT_NAME = 'PRIMARY' AND COLUMN_NAME IN ('chain_id', 'address', 'tx_hash', 'tx_timestamp') AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE tx_lists DROP PRIMARY KEY, ADD PRIMARY KEY (`chain_id`, `address`, `tx_hash`);
-- rollback ALTER TABLE tx_lists DROP PRIMARY KEY, ADD PRIMARY KEY (`chain_id`, `address`, `tx_hash`, `tx_timestamp`);

-- changeset alan:fix-order-index:20240422:1
-- preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='asset_pro_orders' AND index_name='idx_created_at' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_created_at ON asset_pro_orders(created_at);
-- rollback DROP INDEX idx_created_at ON asset_pro_orders;

-- changeset alan:fix-order-index:20240422:2
-- preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='asset_pro_orders' AND index_name='idx_total_cost' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_total_cost ON asset_pro_orders(total_cost);
-- rollback DROP INDEX idx_total_cost ON asset_pro_orders;

-- changeset alan:fix-order-index:20240422:3
-- preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='asset_pro_products' AND index_name='idx_price' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_price ON asset_pro_products(price);
-- rollback DROP INDEX idx_price ON asset_pro_products;

-- changeset alan:fix-order-index:20240422:4
-- preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='asset_pro_products' AND index_name='idx_stock' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_stock ON asset_pro_products(stock);
-- rollback DROP INDEX idx_stock ON asset_pro_products;

-- changeset harry:KW-3342:20240424:1
CREATE TABLE IF NOT EXISTS `studio_organization_gas_swap_tokens` (
  `id` int auto_increment,
  `organization_id` int NOT NULL,
  `chain_id` varchar(20) NOT NULL,
  `contract_address` varchar(60) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`organization_id`) REFERENCES studio_organizations(id),
  INDEX `idx_org_id` (`organization_id`),
  INDEX `idx_chain_id_contract_address` (`chain_id`,`contract_address`)
);
CREATE TABLE IF NOT EXISTS `studio_organization_gas_swaps` (
  `id` int auto_increment,
  `organization_id` int NOT NULL,
  `uid` varchar(60),
  `chain_id` varchar(20) NOT NULL,
  `from` varchar(62) NOT NULL,
  `token_address` varchar(62) NOT NULL,
  `amount` varchar(20) NOT NULL,
  `receive_wallet` varchar(62) NOT NULL,
  `signed_txs` json NOT NULL,
  `estimated_receive` decimal(22,9) NOT NULL,
  `estimated_cost` decimal(22,9) NOT NULL,
  `gas_faucet_tx_hash` varchar(88),
  `user_approve_tx_hash` varchar(88),
  `gas_swap_tx_hash` varchar(88),
  `status` enum('processing','success','failed') NOT NULL,
  `actual_receive` decimal(22,9),
  `actual_cost` decimal(22,9),
  `retry_count` INT NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`organization_id`) REFERENCES studio_organizations(id),
  INDEX `idx_uid` (`uid`),
  INDEX `idx_status` (`status`),
  INDEX `idx_org_id` (`organization_id`)
);
-- rollback DROP TABLE `studio_organization_gas_swap_tokens`; DROP TABLE `studio_organization_gas_swaps`;

-- changeset harry:KW-3342:20240430:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'estimated_receive_usd' and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'gas_faucet_tx_amount' and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'gas_swap_tx_amount' and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'estimated_finish_at' and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gas_swaps ADD COLUMN estimated_receive_usd DECIMAL(22,9);
ALTER TABLE studio_organization_gas_swaps ADD COLUMN gas_faucet_tx_amount DECIMAL(22,9);
ALTER TABLE studio_organization_gas_swaps ADD COLUMN gas_swap_tx_amount DECIMAL(22,9);
ALTER TABLE studio_organization_gas_swaps ADD COLUMN estimated_finish_at TIMESTAMP NOT NULL;
-- rollback ALTER TABLE studio_organization_gas_swaps DROP COLUMN estimated_receive_usd, DROP COLUMN gas_faucet_tx_amount, DROP COLUMN gas_swap_tx_amount;

-- changeset raiven:20240503:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'chain_id' and TABLE_NAME = 'asset_pro_product_base_infos' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'name' and TABLE_NAME = 'asset_pro_product_base_infos' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
UPDATE asset_pro_product_base_infos SET chain_id = 'bsc' WHERE name IN ('USDT/BNB', 'USDC/BNB');
-- rollback UPDATE asset_pro_product_base_infos SET chain_id = 'eth' WHERE name IN ('USDT/BNB', 'USDC/BNB');

-- changeset jason:20240515:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'asset_pro_approval_config' and TABLE_NAME = 'studio_organizations' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `studio_organizations` ADD `asset_pro_approval_config` ENUM('trader','trader-approver-finance_manager','trader-approver');
-- rollback ALTER TABLE `studio_organizations` DROP `asset_pro_approval_config`;

-- changeset jason:20240515:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'serial_id' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'approver_uid' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'finance_manager_uid' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'rejection_note' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'submission_note' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'submission_attachments' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'update_time' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'approve_time' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'release_time' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'tx_hashes' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `assetpro_tx_logs` ADD `serial_id` varchar(56);
ALTER TABLE `assetpro_tx_logs` ADD `approver_uid` varchar(60);
ALTER TABLE `assetpro_tx_logs` ADD `finance_manager_uid` varchar(60);
ALTER TABLE `assetpro_tx_logs` ADD `rejection_note` text;
ALTER TABLE `assetpro_tx_logs` ADD `submission_note` text;
ALTER TABLE `assetpro_tx_logs` ADD `submission_attachments` json;
ALTER TABLE `assetpro_tx_logs` ADD `update_time` int NOT NULL DEFAULT 0;
ALTER TABLE `assetpro_tx_logs` ADD `approve_time` int;
ALTER TABLE `assetpro_tx_logs` ADD `release_time` int;
ALTER TABLE `assetpro_tx_logs` ADD `tx_hashes` json;
UPDATE `assetpro_tx_logs` SET `update_time` = `submit_time`;
UPDATE `assetpro_tx_logs` SET `tx_hashes` = JSON_ARRAY(`tx_hash`) WHERE `tx_hash` IS NOT NULL;
ALTER TABLE `assetpro_tx_logs` ADD CONSTRAINT `fk_assetpro_tx_logs_approver` FOREIGN KEY (organization_id, approver_uid) REFERENCES studio_users (organization_id, uid);
ALTER TABLE `assetpro_tx_logs` ADD CONSTRAINT `fk_assetpro_tx_logs_finance_manager` FOREIGN KEY (organization_id, finance_manager_uid) REFERENCES studio_users (organization_id, uid);
-- rollback ALTER TABLE `assetpro_tx_logs` DROP `serial_id`, DROP `approver_uid`, DROP `finance_manager_uid`, DROP `rejection_note`, DROP `submission_note`, DROP `submission_attachments`, DROP `update_time`, DROP `approve_time`, DROP `release_time`, DROP `tx_hashes`;

-- changeset jason:20240515:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'status' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `assetpro_tx_logs` ADD COLUMN temp_status ENUM('awaiting_approval', 'awaiting_release', 'sending', 'send_success', 'send_failed', 'rejected');
UPDATE assetpro_tx_logs
SET temp_status = CASE
    WHEN status = 'init' THEN 'awaiting_approval'
    WHEN status = 'pending' THEN 'sending'
    ELSE status
END;
ALTER TABLE `assetpro_tx_logs` DROP COLUMN status;
ALTER TABLE `assetpro_tx_logs` CHANGE COLUMN temp_status status ENUM('awaiting_approval', 'awaiting_release', 'sending', 'send_success', 'send_failed', 'rejected') NOT NULL;
-- rollback ALTER TABLE `assetpro_tx_logs` ADD COLUMN temp_status ENUM('init', 'awaiting_release', 'pending', 'send_success', 'send_failed', 'rejected') DEFAULT 'init';
-- rollback UPDATE assetpro_tx_logs SET temp_status = CASE WHEN status = 'awaiting_approval' THEN 'init' WHEN status = 'sending' THEN 'pending' ELSE status END;
-- rollback ALTER TABLE `assetpro_tx_logs` DROP COLUMN status;
-- rollback ALTER TABLE `assetpro_tx_logs` CHANGE COLUMN temp_status status ENUM('init', 'pending', 'send_success', 'send_failed') NOT NULL;

-- changeset jason:KW-3302:20240408:1
CREATE TABLE users (
    uid VARCHAR(60) PRIMARY KEY,
    email VARCHAR(320),
    display_name text,
    phone_number VARCHAR(15),
    referral_code VARCHAR(10) NOT NULL,
    ss_create_time timestamp,
    alchemy_notify_address VARCHAR(62),
    device_identifier VARCHAR(255),
    bio TEXT,
    twitter VARCHAR(255),
    youtube VARCHAR(255),
    instagram VARCHAR(255),
    discord VARCHAR(255),
    custom_link VARCHAR(255),
    hide_spam_nft BOOLEAN,
    has_linked_google BOOLEAN,
    is_random_password BOOLEAN,
    bitcoin_address VARCHAR(62),
    ethereum_address VARCHAR(62),
    solana_address VARCHAR(62),
    tron_address VARCHAR(62),
    encrypt_salt VARCHAR(255),
    encrypted_mnemonic VARCHAR(255),
    kyc_state VARCHAR(20),
    password VARCHAR(255),
    password_salt VARCHAR(255),
    password_salt_frontend VARCHAR(255),
    share_key VARCHAR(255),
    arb VARCHAR(62) NOT NULL,
    bsc VARCHAR(62) NOT NULL,
    btc VARCHAR(62) NOT NULL,
    eth VARCHAR(62) NOT NULL,
    kcc VARCHAR(62) NOT NULL,
    matic VARCHAR(62) NOT NULL,
    sol VARCHAR(62) NOT NULL,
    tron VARCHAR(62) NOT NULL,
    ronin VARCHAR(62) NOT NULL,
    oasys VARCHAR(62) NOT NULL,
    retrieve_mnemonic tinyint(1),
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at timestamp,
    INDEX idx_email (email),
    INDEX idx_phone_number (phone_number),
    INDEX idx_retrieve_mnemonic (retrieve_mnemonic)
);
CREATE TABLE wallet_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_uid VARCHAR(60) NOT NULL,
    create_timestamp timestamp,
    import_timestamp timestamp,
    encrypted_seed_phrase VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    icon_url text NOT NULL,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid),
    INDEX idx_user_uid_encrypted_seed_phrase (user_uid, encrypted_seed_phrase),
    INDEX idx_user_uid (user_uid)
);
CREATE TABLE wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_uid VARCHAR(60) NOT NULL,
    address VARCHAR(62) NOT NULL,
    address_index INT NOT NULL,
    create_timestamp timestamp,
    display_name VARCHAR(255) NOT NULL,
    encrypted_private_key VARCHAR(255) NOT NULL,
    import_timestamp timestamp,
    chain VARCHAR(10) NOT NULL,
    icon_url text NOT NULL,
    wallet_type varchar(20) NOT NULL,
    wallet_group_id INT,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid),
    FOREIGN KEY (wallet_group_id) REFERENCES wallet_groups(id),
    INDEX idx_user_uid_address (user_uid, address),
    INDEX idx_user_uid (user_uid)
);
CREATE TABLE fcm_token_map (
    user_uid VARCHAR(60) PRIMARY KEY,
    client_id VARCHAR(50) NOT NULL,
    token VARCHAR(255) NOT NULL,
    timestamp timestamp NOT NULL,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
CREATE TABLE avatars (
    user_uid VARCHAR(60) PRIMARY KEY,
    avatar_url text NOT NULL,
    chain_id VARCHAR(30) NOT NULL,
    contract_address VARCHAR(255) NOT NULL,
    token_id VARCHAR(255) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid),
    INDEX idx_chain_id_contract_address_token_id (chain_id, contract_address, token_id)
);
CREATE TABLE locale_map (
    user_uid VARCHAR(60) PRIMARY KEY,
    client_id VARCHAR(50) NOT NULL,
    locale VARCHAR(10) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
CREATE TABLE read_all_time_stamp_map (
    user_uid VARCHAR(60) PRIMARY KEY,
    client_id VARCHAR(50) NOT NULL,
    timestamp timestamp NOT NULL,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
CREATE TABLE has_synced_assets (
    user_uid VARCHAR(60) PRIMARY KEY,
    address VARCHAR(62) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
CREATE TABLE privacy_policy_agreements (
    user_uid VARCHAR(60) PRIMARY KEY,
    agreement_time timestamp NOT NULL,
    agreement_version VARCHAR(255) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
CREATE TABLE vault_data_list (
    user_uid VARCHAR(60) PRIMARY KEY,
    account_public_key VARCHAR(255) NOT NULL,
    encrypt_salt VARCHAR(255) NOT NULL,
    encrypted_account_private_key VARCHAR(255) NOT NULL,
    hash_version VARCHAR(255) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
CREATE TABLE google_access_tokens (
    user_uid VARCHAR(60) PRIMARY KEY,
    access_token VARCHAR(255) NOT NULL,
    refresh_token VARCHAR(255) NOT NULL,
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_uid) REFERENCES users(uid)
);
-- rollback DROP TABLE google_access_tokens; DROP TABLE vault_data_list; DROP TABLE privacy_policy_agreements; DROP TABLE has_synced_assets; DROP TABLE read_all_time_stamp_map; DROP TABLE locale_map; DROP TABLE avatars; DROP TABLE fcm_token_map; DROP TABLE wallets; DROP TABLE wallet_groups; DROP TABLE users;

-- changeset jason:KW-3302:********:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'encrypted_mnemonic' and TABLE_NAME = 'users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'encrypted_seed_phrase' and TABLE_NAME = 'wallet_groups' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'share_key' and TABLE_NAME = 'users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE users modify column encrypted_mnemonic varchar(500);
ALTER TABLE wallet_groups modify column encrypted_seed_phrase varchar(500) not null;
ALTER TABLE users modify column share_key varchar(500);
-- rollback ALTER TABLE users modify column encrypted_mnemonic varchar(255);
-- rollback ALTER TABLE wallet_groups modify column encrypted_seed_phrase varchar(255) not null;
-- rollback ALTER TABLE users modify column share_key varchar(255);

-- changeset jason:KW-3302:20240521:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'app_version' and TABLE_NAME = 'users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE users ADD COLUMN app_version VARCHAR(20);
-- rollback ALTER TABLE users DROP COLUMN app_version;

-- changeset jason:KW-3302:20240521:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(1) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='wallet_groups' AND INDEX_NAME='idx_user_uid_encrypted_seed_phrase' LIMIT 1;
DROP INDEX idx_user_uid_encrypted_seed_phrase ON wallet_groups;
-- rollback CREATE INDEX idx_user_uid_encrypted_seed_phrase ON wallet_groups (user_uid, encrypted_seed_phrase);

-- changeset jason:KW-3302:20240527:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(1) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='wallets' AND INDEX_NAME='idx_user_uid_address' LIMIT 1;
DROP INDEX idx_user_uid_address ON wallets;
CREATE UNIQUE INDEX uniq_user_uid_address_chain ON wallets (user_uid, address, chain);
-- rollback DROP INDEX uniq_user_uid_address_chain ON wallets; CREATE INDEX idx_user_uid_address ON wallets (user_uid, address);

-- changeset jason:KW-3302:20240527:2
CREATE UNIQUE INDEX uniq_user_uid_address_chain_wallet_group_id ON wallets (user_uid, address, chain, wallet_group_id);
-- rollback DROP INDEX uniq_user_uid_address_chain_wallet_group_id ON wallets;

-- changeset jason:hotfix:20240530:1
ALTER TABLE assets MODIFY name VARCHAR(250) NOT NULL;
-- rollback ALTER TABLE assets MODIFY name VARCHAR(150) NOT NULL;

-- changeset jason:KW-3302:20240530:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(1) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='wallets' AND INDEX_NAME='idx_user_uid_address' LIMIT 1;
CREATE INDEX idx_user_uid_address ON wallets (user_uid, address);
-- rollback DROP INDEX idx_user_uid_address ON wallets;

-- changeset raiven:insert-finance-manager:20240603:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM wallet.studio_roles WHERE (module, name) = ('asset_pro', 'finance_manager');
INSERT INTO `wallet`.`studio_roles` (`module`, `name`) VALUES
('asset_pro', 'finance_manager');
-- rollback DELETE FROM studio_roles WHERE (module, name) = ('asset_pro', 'finance_manager');

-- changeset beans:index-for-select-tx-detail:20240604:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='tx_details' AND INDEX_NAME='idx_chain_id_address_block_num_tx_hash';
CREATE INDEX idx_chain_id_address_block_num_tx_hash ON tx_details (chain_id, address, block_num, tx_hash);
-- rollback DROP INDEX idx_chain_id_address_block_num_tx_hash ON tx_details;

-- changeset harry:KW-3553:20240604:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'twilio_verify_sid' and TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `oauth_client_configs` ADD `twilio_verify_sid` varchar(100);
-- rollback ALTER TABLE `oauth_client_configs` DROP `twilio_verify_sid`;

-- changeset harry:KW-3553:20240604:2 context:dev
UPDATE oauth_client_configs SET twilio_verify_sid = 'VAec11f7ac788eafde6b939b64a5fc2c1f' WHERE id = '9c5a79fc1117310f976b53752659b61d';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA82e63f641d37ff88201ba08265526b17' WHERE id = '8e3dbbd857c9ad8a68a97cbc75ff40ac';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VAa3bc6c40b67c446ae6d68522cb23a54f' WHERE id = '9e4ec34a3a39394a';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VAf8c47208feca5697c73bf5be9fdb57e9' WHERE id = 'alpha-gate-wallet';
-- rollback UPDATE oauth_client_configs SET twilio_verify_sid = '' WHERE id in ('9c5a79fc1117310f976b53752659b61d', '8e3dbbd857c9ad8a68a97cbc75ff40ac', '9e4ec34a3a39394a', 'alpha-gate-wallet');

-- changeset harry:KW-3553:20240604:2 context:staging
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA39b3d56dad7820b2df954f95c344ffea' WHERE id = '7c8117912ec55102331994275ea3f3a3';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA52b5606af93987d296f269b94c1f744b' WHERE id = '1d3c1e149dcf03e015c1cc42caabe083';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VAa416c62348a4a3ab33c6440595de819a' WHERE id = '5fb177d395036039';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA0f2e576c4792a231f15906af2a0b59a5' WHERE id = 'alpha-gate-wallet';
-- rollback UPDATE oauth_client_configs SET twilio_verify_sid = '' WHERE id in ('7c8117912ec55102331994275ea3f3a3', '1d3c1e149dcf03e015c1cc42caabe083', '5fb177d395036039', 'alpha-gate-wallet');

-- changeset harry:KW-3553:20240604:2 context:prod
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA39b3d56dad7820b2df954f95c344ffea' WHERE id = '20b1905704bf329be7af231723fe30e3';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA52b5606af93987d296f269b94c1f744b' WHERE id = '0c11772efe921d67a5ae56d5ba596a32';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VAa416c62348a4a3ab33c6440595de819a' WHERE id = '4b2108adfdb5401a';
UPDATE oauth_client_configs SET twilio_verify_sid = 'VA0f2e576c4792a231f15906af2a0b59a5' WHERE id = 'alpha-gate-wallet';
-- rollback UPDATE oauth_client_configs SET twilio_verify_sid = '' WHERE id in ('20b1905704bf329be7af231723fe30e3', '0c11772efe921d67a5ae56d5ba596a32', '4b2108adfdb5401a', 'alpha-gate-wallet');

-- changeset jason:KW-3302:20240607:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(1) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='wallets' AND INDEX_NAME='idx_chain' LIMIT 1;
CREATE INDEX idx_chain ON wallets(chain);
-- rollback DROP INDEX idx_chain ON wallets;

-- changeset jason:fix-tx-log-decimal:20240617:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'amount' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `assetpro_tx_logs` MODIFY `amount` DECIMAL(26, 18);
-- rollback ALTER TABLE `assetpro_tx_logs` MODIFY `amount` DECIMAL(12, 4);

-- changeset raiven:KW-3669:20240619:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:2 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('daily_transfer_limit', 'transfer_approval_threshold')) and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
UPDATE studio_users SET daily_transfer_limit = 10.0 WHERE daily_transfer_limit IS NULL;
UPDATE studio_users SET transfer_approval_threshold = 1.0 WHERE transfer_approval_threshold IS NULL;

-- changeset raiven:KW-3669:20240619:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:2 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME in ('daily_transfer_limit', 'transfer_approval_threshold')) and TABLE_NAME = 'studio_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_users MODIFY COLUMN daily_transfer_limit decimal(30,4) UNSIGNED DEFAULT 10.0 NOT NULL;
ALTER TABLE studio_users MODIFY COLUMN transfer_approval_threshold decimal(30,4) UNSIGNED DEFAULT 1.0 NOT NULL;
-- rollback ALTER TABLE studio_users DROP COLUMN daily_transfer_limit, transfer_approval_threshold;

-- changeset harry:KW-3652:20240618:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'studio_organization_gas_faucets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE `studio_organization_gas_faucets` (
  `id` integer auto_increment,
  `organization_id` integer NOT NULL,
  `uid` varchar(60) NOT NULL,
  `chain_id` varchar(20) NOT NULL,
  `receive_wallet` varchar(62) NOT NULL,
  `gas_amount` bigint NOT NULL,
  `gas_price_gwei` decimal(18,9),
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('success','failed','unknown'),
  `tx_hash` varchar(88),
  `total_cost` decimal(22,9),
  `total_cost_usd` decimal(22,9),
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY (`id`),
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
  INDEX `idx_receive_wallet` (`receive_wallet`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_org_id` (`organization_id`),
  INDEX `idx_org_id_receive_wallet_created_at` (`organization_id`, `receive_wallet`, `created_at`),
  INDEX `idx_org_id_created_at` (`organization_id`,`created_at`)
);
-- rollback DROP TABLE `studio_organization_gas_faucets`;

-- changeset beans:KW-3713:20240622:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_user_amounts' AND index_name='idx_nft_user_amounts_chain_contract_token' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_user_amounts_chain_contract_token ON nft_user_amounts(chain_id, contract_address, token_id);
-- rollback DROP INDEX idx_nft_user_amounts_chain_contract_token ON nft_user_amounts;

-- changeset beans:KW-3713:20240622:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_user_amounts' AND index_name='idx_nft_user_amounts_owner_chain' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_user_amounts_owner_chain ON nft_user_amounts(owner_address, chain_id);
-- rollback DROP INDEX idx_nft_user_amounts_owner_chain ON nft_user_amounts;

-- changeset beans:KW-3713:20240622:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_user_amounts' AND index_name='idx_nft_user_amounts_amount' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_user_amounts_amount ON nft_user_amounts(amount);
-- rollback DROP INDEX idx_nft_user_amounts_amount ON nft_user_amounts;

-- changeset beans:KW-3713:20240622:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_user_amounts' AND index_name='idx_nft_user_amounts_tx_timestamp' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_user_amounts_tx_timestamp ON nft_user_amounts(tx_timestamp);
-- rollback DROP INDEX idx_nft_user_amounts_tx_timestamp ON nft_user_amounts;

-- changeset beans:KW-3713:20240622:5
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='idx_nft_assets_chain_contract_token' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_assets_chain_contract_token ON nft_assets(chain_id, contract_address, token_id);
-- rollback DROP INDEX idx_nft_assets_chain_contract_token ON nft_assets;

-- changeset beans:KW-3713:20240622:6
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='idx_nft_assets_collection_slug' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_assets_collection_slug ON nft_assets(collection_slug);
-- rollback DROP INDEX idx_nft_assets_collection_slug ON nft_assets;

-- changeset beans:KW-3713:20240622:7
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_assets' AND index_name='idx_nft_assets_is_spam' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_assets_is_spam ON nft_assets(is_spam);
-- rollback DROP INDEX idx_nft_assets_is_spam ON nft_assets;

-- changeset beans:KW-3713:20240622:8
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='nft_collections' AND index_name='idx_nft_collections_slug' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_nft_collections_slug ON nft_collections(slug);
-- rollback DROP INDEX idx_nft_collections_slug ON nft_collections;

-- changeset raiven:KW-3412:20240625:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos WHERE name IN ('USDT/ARB', 'USDC/ARB');
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image, quote_currency_logo) VALUES
('USDT/ARB', 'arb', 'USDT', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdt_arbitrum.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png'),
('USDC/ARB', 'arb', 'USDC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/usdc_arbitrum.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('USDT/ARB', 'USDC/ARB');

-- changeset raiven:KW-3412:20240625:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='asset_pro_products' AND index_name='uniq_asset_pro_products_org_and_base_info' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE UNIQUE INDEX uniq_asset_pro_products_org_and_base_info ON asset_pro_products(organization_id, product_base_info_id);
-- rollback ALTER TABLE asset_pro_products DROP KEY uniq_asset_pro_products_org_and_base_info;

-- changeset raiven:KW-3412:20240626:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:2 SELECT count(*) FROM asset_pro_product_base_infos WHERE name IN ('USDT/ARB', 'USDC/ARB');
UPDATE asset_pro_product_base_infos SET image = 'https://wallet-static.kryptogo.com/public/assets/images/usdt_arb.png' WHERE name = 'USDT/ARB';
UPDATE asset_pro_product_base_infos SET image = 'https://wallet-static.kryptogo.com/public/assets/images/usdc_arb.png' WHERE name = 'USDC/ARB';
-- rollback UPDATE asset_pro_product_base_infos SET image = 'https://wallet-static.kryptogo.com/public/assets/images/usdt_arbitrum.png' WHERE name = 'USDT/ARB';
-- rollback UPDATE asset_pro_product_base_infos SET image = 'https://wallet-static.kryptogo.com/public/assets/images/usdc_arbitrum.png' WHERE name = 'USDC/ARB';

-- changeset harry:KW-3725:20240626:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'studio_organization_aave_fees' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE `studio_organization_aave_fees` (
  `id` integer auto_increment,
  `organization_id` integer NOT NULL,
  `uid` varchar(60) NOT NULL,
  `chain_id` varchar(20) NOT NULL,
  `tx_hash` varchar(88) NOT NULL,
  `wallet_address` varchar(62) NOT NULL,
  `token` varchar(60) NOT NULL,
  `amount` decimal(22,9) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY (`id`),
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
  INDEX `idx_org_chain_wallet_token` (`organization_id`,`chain_id`,`wallet_address`,`token`),
  CONSTRAINT `uni_studio_organization_aave_fees_tx_hash` UNIQUE (`tx_hash`)
);
-- rollback DROP TABLE `studio_organization_aave_fees`;

-- changeset raiven:KW-3623:20240703:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'icon_url' and TABLE_NAME = 'studio_organizations' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organizations ADD icon_url text;
-- rollback ALTER TABLE studio_organizations DROP COLUMN icon_url;

-- changeset raiven:KW-3623:20240703:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('telegram', 'discord', 'twitter') and TABLE_NAME = 'studio_markets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_markets ADD telegram text;
ALTER TABLE studio_markets ADD discord text;
ALTER TABLE studio_markets ADD twitter text;
-- rollback ALTER TABLE studio_markets DROP COLUMN telegram, discord, twitter;

-- changeset raiven:KW-3623:20240703:3
--preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'UNIQUE' AND TABLE_NAME = 'studio_markets' AND CONSTRAINT_NAME = 'uniq_studio_markets_title' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_markets ADD UNIQUE INDEX uniq_studio_markets_title(title);
-- rollback ALTER TABLE studio_markets ALTER TABLE studio_markets DROP INDEX uniq_studio_markets_title;

-- changeset jason:hotfix:20240709:1
ALTER TABLE users MODIFY phone_number VARCHAR(25);
-- rollback ALTER TABLE users MODIFY phone_number VARCHAR(15);

-- changeset jason:hotfix:20240709:2
ALTER TABLE users MODIFY referral_code VARCHAR(15) NOT NULL;
-- rollback ALTER TABLE users MODIFY referral_code VARCHAR(10) NOT NULL;

-- changeset harry:KW-3746:20240709:1
CREATE TABLE IF NOT EXISTS `studio_organization_gasless_sends` (
  `id` integer auto_increment,
  `organization_id` integer NOT NULL,
  `uid` varchar(60) NOT NULL,
  `chain_id` varchar(20) NOT NULL,
  `from` varchar(62) NOT NULL,
  `recipient` varchar(62) NOT NULL,
  `token_address` varchar(62) NOT NULL,
  `amount` varchar(20) NOT NULL,
  `fee` varchar(20) NOT NULL,
  `signed_txs` json NOT NULL,
  `status` enum('processing', 'success', 'failed') NOT NULL,
  `fee_usd` decimal(22, 9) NOT NULL,
  `estimated_finish_at` TIMESTAMP NOT NULL,
  `energy_rent_cost` decimal(22, 9),
  `actual_cost_usd` decimal(22, 9),
  `gas_faucet_tx_hash` varchar(88),
  `user_approve_tx_hash` varchar(88),
  `gasless_send_tx_hash` varchar(88),
  `retry_count` integer NOT NULL DEFAULT 0,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`organization_id`) REFERENCES studio_organizations(id),
  INDEX `idx_org_id` (`organization_id`),
  INDEX `idx_uid` (`uid`)
);
-- rollback DROP TABLE `studio_organization_gasless_sends`;

-- changeset harry:hotfix-asset-name:20240721:1
ALTER TABLE assets MODIFY name VARCHAR(1024) NOT NULL;
-- rollback ALTER TABLE assets MODIFY name VARCHAR(250) NOT NULL;

-- changeset harry:hotfix-asset-symbol:20240721:2
ALTER TABLE assets MODIFY symbol VARCHAR(512) NULL;
-- rollback ALTER TABLE assets MODIFY symbol VARCHAR(100) NULL;

-- changeset harry:ST-27:20240720:1
CREATE TABLE IF NOT EXISTS `historical_user_balances` (
  `uid` varchar(60) NOT NULL,
  `chain_id` varchar(100),
  `wallet_address` varchar(100) NOT NULL,
  `wallet_type` varchar(20) NOT NULL,
  `balance` decimal(25, 9) NOT NULL,
  `stablecoin_balance` decimal(25, 9) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`, `chain_id`, `wallet_address`),
  INDEX `idx_chain_id_wallet_address` (`chain_id`, `wallet_address`),
  INDEX `idx_created_at` (`created_at`)
);
-- rollback DROP TABLE `historical_user_balances`;

-- changeset jason:KW-3881:20240716:1
CREATE TABLE IF NOT EXISTS `asset_pro_liquidities` (
  `id` integer auto_increment,
  `organization_id` integer NOT NULL,
  `liquidity_type` enum('buy_crypto', 'gas_swap') NOT NULL,
  `chain_id` varchar(20) NOT NULL,
  `contract_address` varchar(62) NOT NULL,
  `symbol` varchar(10) NOT NULL,
  `token_url` text NOT NULL,
  `unit` varchar(10) NOT NULL,
  `profit_marigin_rate` decimal(6,5) NOT NULL,
  `alert_threshold` decimal(12,0) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp,
  PRIMARY KEY (`id`),
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
  INDEX `idx_org_id` (`organization_id`)
);
-- rollback DROP TABLE `asset_pro_liquidities`;

-- changeset jason:fix-schema-unsync:20240722:1 context:dev
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `attributes` json;
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `image_url` text;
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `external_url` text;
ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `animation_url` text;
ALTER TABLE `dashboard_prizes` MODIFY COLUMN `redeem_url` text;
ALTER TABLE `notifications` MODIFY COLUMN `preview_image_url` text NOT NULL;
ALTER TABLE `studio_exchange_rate` MODIFY COLUMN `image_url` text;
ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `image_url` text NOT NULL;
ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `site_url` text NOT NULL;
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `attributes` text;
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `image_url` varchar(255);
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `external_url` varchar(255);
-- rollback ALTER TABLE `airdrop_nft_traits` MODIFY COLUMN `animation_url` varchar(255);
-- rollback ALTER TABLE `dashboard_prizes` MODIFY COLUMN `redeem_url` varchar(255);
-- rollback ALTER TABLE `notifications` MODIFY COLUMN `preview_image_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `studio_exchange_rate` MODIFY COLUMN `image_url` varchar(255);
-- rollback ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `image_url` varchar(255) NOT NULL;
-- rollback ALTER TABLE `studio_organization_dapps` MODIFY COLUMN `site_url` varchar(255) NOT NULL;

-- changeset jason:fix-schema-unsync:20240722:1 context:prod
DROP INDEX idx_user_uid_encrypted_seed_phrase ON wallet_groups;
CREATE UNIQUE INDEX uniq_user_uid_address_chain ON wallets (user_uid, address, chain);
-- rollback CREATE INDEX idx_user_uid_encrypted_seed_phrase ON wallet_groups (user_uid, encrypted_seed_phrase);
-- rollback DROP INDEX uniq_user_uid_address_chain ON wallets;

-- changeset jason:fix-liquidities-schema:20240723:1
ALTER TABLE `asset_pro_liquidities` MODIFY COLUMN `contract_address` varchar(62);
ALTER TABLE `asset_pro_liquidities` MODIFY COLUMN `alert_threshold` decimal(12, 0);
-- rollback ALTER TABLE `asset_pro_liquidities` MODIFY COLUMN `contract_address` varchar(62) NOT NULL;
-- rollback ALTER TABLE `asset_pro_liquidities` MODIFY COLUMN `alert_threshold` decimal(12,0) NOT NULL;

-- changeset jason:fix-liquidities-schema:20240723:2
ALTER TABLE `asset_pro_liquidities` RENAME COLUMN `profit_marigin_rate` TO `profit_margin_rate`;
-- rollback ALTER TABLE `asset_pro_liquidities` RENAME COLUMN `profit_margin_rate` TO `profit_marigin_rate`;

-- changeset harry:fix-historical-user-balances-schema:20240724:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'historical_user_balances' AND CONSTRAINT_TYPE = 'PRIMARY KEY';
ALTER TABLE `historical_user_balances` DROP PRIMARY KEY;
CREATE INDEX idx_uid_chain_id_wallet_address ON historical_user_balances(uid, chain_id, wallet_address);
-- rollback ALTER TABLE `historical_user_balances` ADD PRIMARY KEY (`uid`, `chain_id`, `wallet_address`);
-- rollback DROP INDEX idx_uid_chain_id_wallet_address ON historical_user_balances;

-- changeset harry:ST-21:20240726:1
CREATE TABLE IF NOT EXISTS ephemeral_owners (
    address VARCHAR(42) PRIMARY KEY,
    private_key VARBINARY(32) NOT NULL,
    is_using BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_is_using (is_using)
);
CREATE TABLE IF NOT EXISTS ephemeral_notes (
    id VARCHAR(12) PRIMARY KEY,
    chain_id VARCHAR(20) NOT NULL,
    from_address VARCHAR(42) NOT NULL,
    token_address VARCHAR(42) NOT NULL,
    amount VARCHAR(78) NOT NULL,
    deposit_tx_hash VARCHAR(66) NOT NULL,
    ephemeral_owner VARCHAR(42) NOT NULL,
    status ENUM('active', 'claimed', 'cancelled') NOT NULL,
    claim_tx_hash VARCHAR(66),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE INDEX idx_ephemeral_notes_deposit_tx_hash (deposit_tx_hash),
    INDEX idx_from_address (from_address),
    INDEX idx_ephemeral_owner (ephemeral_owner),
    INDEX idx_status (status)
);
-- rollback DROP TABLE ephemeral_owners; DROP TABLE ephemeral_notes;

-- changeset raiven:KW-3809:20240723:1
-- preconditions onFail:CONTINUE
CREATE TABLE IF NOT EXISTS `crypto_tos_contents` (
  `id` integer auto_increment,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `organization_id` integer NOT NULL,
  `version` varchar(8) NOT NULL,
  `content` text NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE
);
CREATE UNIQUE INDEX uniq_crypto_tos_contents_org_id_version ON crypto_tos_contents (organization_id, version);
INSERT INTO crypto_tos_contents (organization_id, version, content) values (1, 'v1', '- KryptoGO is a blockchain wallet service provider, not an exchange. We don''t have your crypto assets at all. Just want to make sure you can buy crypto with ease. \n - We only provide the "buy crypto" service to users of KryptoGO Wallet who have completed the user verification (KYC).\n - After you complete the payment, the crypto you bought will be sent to your blockchain wallet in 30 minutes, and you can send it to other blockchain wallets at anytime & anywhere without review. \n - If you have any questions, check the FAQ on [Support center > buy crypto](https://support.kryptogo.com/hc/zh-tw/sections/4411678520719-%E9%8C%A2%E5%8C%85-%E5%87%BA%E5%85%A5%E9%87%91) fag or contact us on Discord or email us.');
-- rollback DROP INDEX uniq_crypto_tos_contents_org_id_version ON crypto_tos_contents;
-- rollback DROP TABLE `crypto_tos_contents`;

-- changeset raiven:KW-3809:20240723:2 context:dev
-- preconditions onFail:CONTINUE
INSERT INTO crypto_tos_contents (organization_id, version, content) values (16, 'v1', 'TongBao is a blockchain wallet service provider, not an exchange. We don''t have your crypto assets at all. Just want to make sure you can buy crypto with ease. \n - We only provide the "buy crypto" service to users of TongBao Wallet who have completed the user verification (KYC).\n - After you complete the payment, the crypto you bought will be sent to your blockchain wallet in 30 minutes, and you can send it to other blockchain wallets at anytime & anywhere without review. \n - If you have any questions, check the FAQ on [Support center > buy crypto](https://alphagategroup.com/) fag or contact us on Discord or email us.');
-- rollback DELETE FROM crypto_tos_contents WHERE organization_id = 16 AND version = 'v1';

-- changeset raiven:KW-3809:20240723:2 context:staging
-- preconditions onFail:CONTINUE
INSERT INTO crypto_tos_contents (organization_id, version, content) values (8, 'v1', '- TongBao is a blockchain wallet service provider, not an exchange. We don''t have your crypto assets at all. Just want to make sure you can buy crypto with ease. \n - We only provide the "buy crypto" service to users of TongBao Wallet who have completed the user verification (KYC).\n - After you complete the payment, the crypto you bought will be sent to your blockchain wallet in 30 minutes, and you can send it to other blockchain wallets at anytime & anywhere without review. \n - If you have any questions, check the FAQ on [Support center > buy crypto](https://alphagategroup.com/) fag or contact us on Discord or email us.');
-- rollback DELETE FROM crypto_tos_contents WHERE organization_id = 8 AND version = 'v1';

-- changeset raiven:KW-3809:20240723:2 context:prod
-- preconditions onFail:CONTINUE
INSERT INTO crypto_tos_contents (organization_id, version, content) values (16, 'v1', 'TongBao is a blockchain wallet service provider, not an exchange. We don''t have your crypto assets at all. Just want to make sure you can buy crypto with ease. \n - We only provide the "buy crypto" service to users of TongBao Wallet who have completed the user verification (KYC).\n - After you complete the payment, the crypto you bought will be sent to your blockchain wallet in 30 minutes, and you can send it to other blockchain wallets at anytime & anywhere without review. \n - If you have any questions, check the FAQ on [Support center > buy crypto](https://alphagategroup.com/) fag or contact us on Discord or email us.');
-- rollback DELETE FROM crypto_tos_contents WHERE organization_id = 16 AND version = 'v1';

-- changeset raiven:KW-3809:20240723:3
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('organization_id') AND TABLE_NAME = 'crypto_tos_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE crypto_tos_logs ADD COLUMN crypto_tos_content_id INT NOT NULL DEFAULT 1;
ALTER TABLE crypto_tos_logs
  MODIFY COLUMN crypto_tos_content_id INT NOT NULL,
  ADD FOREIGN KEY (crypto_tos_content_id) REFERENCES crypto_tos_contents(id) ON DELETE CASCADE;
-- rollback ALTER TABLE crypto_tos_logs DROP COLUMN crypto_tos_content_id, DROP FOREIGN KEY crypto_tos_content_id;

-- changeset raiven:KW-3809:20240723:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('privacy_policy_link') AND TABLE_NAME = 'oauth_client_configs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE oauth_client_configs ADD COLUMN privacy_policy_link VARCHAR(255) NOT NULL;
UPDATE oauth_client_configs SET privacy_policy_link = 'https://www.kryptogo.com/company/legal' WHERE name = 'KryptoGO';
UPDATE oauth_client_configs SET privacy_policy_link = 'https://alphagategroup.com/' WHERE id = 'alpha-gate-wallet';
-- rollback ALTER TABLE oauth_client_configs DROP COLUMN privacy_policy_link;

-- changeset jason:liquidities-seeding:20240730:1 context:dev
INSERT INTO asset_pro_liquidities (organization_id, liquidity_type, chain_id, contract_address, symbol, token_url, unit, profit_margin_rate, alert_threshold)
VALUES
(1, 'buy_crypto', 'tron', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png', 'USD', 0.01830, NULL),
(1, 'gas_swap', 'tron', NULL, 'TRX', 'https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'USDT', 0.02430, 100),
(16, 'buy_crypto', 'tron', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png', 'USD', 0.01830, NULL),
(16, 'gas_swap', 'tron', NULL, 'TRX', 'https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'USDT', 0.02430, 100);
-- rollback DELETE FROM asset_pro_liquidities WHERE organization_id in (1, 16);

-- changeset jason:liquidities-seeding:20240730:1 context:staging
INSERT INTO asset_pro_liquidities (organization_id, liquidity_type, chain_id, contract_address, symbol, token_url, unit, profit_margin_rate, alert_threshold)
VALUES
(1, 'buy_crypto', 'tron', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png', 'USD', 0.01830, NULL),
(1, 'gas_swap', 'tron', NULL, 'TRX', 'https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'USDT', 0.02430, 100),
(8, 'buy_crypto', 'tron', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png', 'USD', 0.01830, NULL),
(8, 'gas_swap', 'tron', NULL, 'TRX', 'https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'USDT', 0.02430, 100);
-- rollback DELETE FROM asset_pro_liquidities WHERE organization_id in (1, 8);

-- changeset jason:liquidities-seeding:20240730:1 context:prod
INSERT INTO asset_pro_liquidities (organization_id, liquidity_type, chain_id, contract_address, symbol, token_url, unit, profit_margin_rate, alert_threshold)
VALUES
(1, 'buy_crypto', 'tron', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png', 'USD', 0.01830, NULL),
(1, 'gas_swap', 'tron', NULL, 'TRX', 'https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'USDT', 0.02430, 100),
(16, 'buy_crypto', 'tron', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_tron.png', 'USD', 0.01830, NULL),
(16, 'gas_swap', 'tron', NULL, 'TRX', 'https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'USDT', 0.02430, 100);
-- rollback DELETE FROM asset_pro_liquidities WHERE organization_id in (1, 16);

-- changeset jason:KW-3884:20240719:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='asset_pro_liquidities' AND index_name='uniq_asset_liquidity' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE UNIQUE INDEX uniq_asset_liquidity ON asset_pro_liquidities(organization_id, liquidity_type, chain_id, contract_address);
-- rollback DROP INDEX uniq_asset_liquidity ON asset_pro_liquidities;

-- changeset jason:KW-3884:20240719:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'profit_margin_rate' and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD profit_margin_rate decimal(6,5);
-- rollback ALTER TABLE orders DROP COLUMN profit_margin_rate;

-- changeset jason:KW-3884:20240719:3
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'profit_margin_rate' and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gas_swaps ADD profit_margin_rate decimal(6,5);
-- rollback ALTER TABLE studio_organization_gas_swaps DROP COLUMN profit_margin_rate;

-- changeset jason:KW-3884:20240719:4
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME in ('client_id', 'organization_id') and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD client_id VARCHAR(50);
ALTER TABLE orders ADD organization_id integer;
ALTER TABLE orders ADD CONSTRAINT fk_orders_organization_id FOREIGN KEY (organization_id) REFERENCES studio_organizations(id);
-- rollback ALTER TABLE orders DROP COLUMN client_id, DROP COLUMN organization_id;

-- changeset jason:KW-3884:20240719:5 context:dev
UPDATE orders SET client_id = '9c5a79fc1117310f976b53752659b61d' WHERE client_id IS NULL OR client_id = '';
UPDATE orders SET organization_id = 1 WHERE organization_id IS NULL OR organization_id = 0;
-- rollback UPDATE orders SET client_id = '' WHERE client_id = '';
-- rollback UPDATE orders SET organization_id = '' WHERE organization_id = 1;

-- changeset jason:KW-3884:20240719:5 context:staging
UPDATE orders SET client_id = '7c8117912ec55102331994275ea3f3a3' WHERE client_id IS NULL OR client_id = '';
UPDATE orders SET organization_id = 1 WHERE organization_id IS NULL OR organization_id = 0;
-- rollback UPDATE orders SET client_id = '' WHERE client_id = '';
-- rollback UPDATE orders SET organization_id = '' WHERE organization_id = 1;

-- changeset jason:KW-3884:20240719:5 context:prod
UPDATE orders SET client_id = '20b1905704bf329be7af231723fe30e3' WHERE client_id IS NULL OR client_id = '';
UPDATE orders SET organization_id = 1 WHERE organization_id IS NULL OR organization_id = 0;
-- rollback UPDATE orders SET client_id = '' WHERE client_id = '';
-- rollback UPDATE orders SET organization_id = '' WHERE organization_id = 1;

-- changeset jason:KW-3884:20240729:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME in ('native_token_price', 'paid_token_price') and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gas_swaps ADD profit_margin decimal(22,9);
ALTER TABLE studio_organization_gas_swaps ADD native_token_price decimal(22,9);
ALTER TABLE studio_organization_gas_swaps ADD paid_token_price decimal(22,9);
-- rollback ALTER TABLE studio_organization_gas_swaps DROP COLUMN profit_margin, DROP COLUMN native_token_price, DROP COLUMN paid_token_price;

-- changeset jason:KW-3884:20240729:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME in ('native_token_price') and TABLE_NAME = 'studio_organization_gasless_sends' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gasless_sends ADD native_token_price decimal(22,9);
-- rollback ALTER TABLE studio_organization_gasless_sends DROP COLUMN native_token_price;

-- changeset jason:KW-3884:20240729:3
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME in ('profit_margin') and TABLE_NAME = 'orders' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE orders ADD profit_share_ratio decimal(3,2);
ALTER TABLE orders ADD profit_margin decimal(22,9);
-- rollback ALTER TABLE orders DROP COLUMN profit_share_ratio, DROP COLUMN profit_margin;

-- changeset harry:ST-21:20240731:1
ALTER TABLE `ephemeral_owners` ADD COLUMN `type` ENUM('evm', 'tron') NOT NULL DEFAULT 'evm';
ALTER TABLE `ephemeral_notes`
  ADD COLUMN `symbol` VARCHAR(20) NOT NULL DEFAULT '',
  ADD COLUMN `token_decimals` TINYINT UNSIGNED NOT NULL DEFAULT 18,
  ADD COLUMN `sender_uid` VARCHAR(36) NOT NULL DEFAULT '';
-- rollback ALTER TABLE `ephemeral_owners` DROP COLUMN `type`;
-- rollback ALTER TABLE `ephemeral_notes` DROP COLUMN `symbol`, DROP COLUMN `token_decimals`, DROP COLUMN `sender_uid`;

-- changeset raiven:KW-3979:20240731:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME = 'asset_pro') and TABLE_NAME = 'studio_organization_modules' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_modules MODIFY COLUMN asset_pro set('treasury','send_token','transaction_history','operators','market','revenue') DEFAULT NULL;
-- rollback ALTER TABLE studio_organization_modules MODIFY COLUMN asset_pro set('treasury','send_token','transaction_history','operators','market') DEFAULT NULL;

-- changeset beans:add-index-tx-job-logs:20240731:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='tx_job_logs' AND index_name='idx_tx_job_logs_created_at' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_tx_job_logs_created_at ON tx_job_logs(created_at);
-- rollback DROP INDEX idx_tx_job_logs_created_at ON tx_job_logs;

-- changeset jason:fix-buy-crypto-liquidity:20240801:1
INSERT INTO asset_pro_liquidities (organization_id, liquidity_type, chain_id, contract_address, symbol, token_url, unit, profit_margin_rate, alert_threshold)
VALUES
(1, 'buy_crypto', 'eth', NULL, 'ETH', 'https://token-icons.s3.amazonaws.com/eth.png', 'USD', 0.01, NULL),
(1, 'buy_crypto', 'matic', NULL, 'MATIC', 'https://token-icons.s3.amazonaws.com/******************************************.png', 'USD', 0.01, NULL),
(1, 'buy_crypto', 'eth', '******************************************', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_eth.png', 'USD', 0.01, NULL),
(1, 'buy_crypto', 'matic', '******************************************', 'USDT', 'https://wallet-static.kryptogo.com/public/assets/images/usdt_matic.png', 'USD', 0.01, NULL);
-- rollback DELETE FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'buy_crypto' AND chain_id <> 'tron';

-- changeset raiven:KW-3884:20240807:4
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME in ('profit_margin') and TABLE_NAME = 'studio_organization_gasless_sends' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gasless_sends ADD profit_margin decimal(22,9);
-- rollback ALTER TABLE studio_organization_gasless_sends DROP COLUMN profit_margin;

-- changeset raiven:KW-4003:20240812:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'compliance_api_key' and TABLE_NAME = 'studio_organizations' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organizations ADD compliance_api_key VARCHAR(255) NULL;
-- rollback ALTER TABLE studio_organizations DROP COLUMN compliance_api_key;

-- changeset harry:fix-fcm-token-update:20240822:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='fcm_token_map' AND CONSTRAINT_TYPE='PRIMARY KEY';
ALTER TABLE fcm_token_map DROP PRIMARY KEY, ADD PRIMARY KEY (user_uid, client_id, token);
-- rollback ALTER TABLE fcm_token_map DROP PRIMARY KEY, ADD PRIMARY KEY (user_uid);

-- changeset raiven:KW-4050:20240826:1
CREATE TABLE IF NOT EXISTS studio_organization_send_with_fee_txs (
	ID INTEGER AUTO_INCREMENT PRIMARY KEY,
	organization_id INTEGER NOT NULL,
	uid VARCHAR(60) NOT NULL,
	tx_hash VARCHAR(255) NOT NULL,
	chain_id VARCHAR(20) NOT NULL,
	token_address VARCHAR(62) NOT NULL,
	profit_margin_rate DECIMAL(6,5),
	`type` ENUM('main_token','token') NOT NULL,
	amount VARCHAR(255),
	sender VARCHAR(62),
	recipient VARCHAR(62),
	fee_address VARCHAR(62),
	fee_amount DECIMAL(22,9),
	profit_margin DECIMAL(22,9),
	token_price DECIMAL(22,9),
	created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
	updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP
);
-- rollback DROP TABLE studio_organization_send_with_fee_txs;

-- changeset harry:ST-94:20240826:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'send_link_campaign_users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE TABLE send_link_campaign_users (
    uid VARCHAR(60) PRIMARY KEY,
    status ENUM('not_started', 'link_created', 'link_shared', 'link_claimed', 'reward_sent') NOT NULL DEFAULT 'not_started',
    announcement_sent BOOLEAN NOT NULL DEFAULT false,
    sender_reward_tx_hash VARCHAR(66),
    recipient_reward_tx_hash VARCHAR(66),
    recipient_uid VARCHAR(60),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_send_link_campaign_users_uid FOREIGN KEY (uid) REFERENCES users(uid),
    CONSTRAINT fk_send_link_campaign_users_recipient_uid FOREIGN KEY (recipient_uid) REFERENCES users(uid)
);
CREATE INDEX idx_send_link_campaign_users_announcement_sent ON send_link_campaign_users(announcement_sent);
CREATE INDEX idx_send_link_campaign_users_status ON send_link_campaign_users(status);
-- rollback DROP INDEX idx_send_link_campaign_users_announcement_sent ON send_link_campaign_users;
-- rollback DROP INDEX idx_send_link_campaign_users_status ON send_link_campaign_users;
-- rollback DROP TABLE send_link_campaign_users;

-- changeset raiven:KW-4050:20240828:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='studio_organization_send_with_fee_txs' AND index_name='uniq_tx_hash' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE UNIQUE INDEX uniq_tx_hash ON studio_organization_send_with_fee_txs(tx_hash);
-- rollback DROP INDEX uniq_tx_hash ON studio_organization_send_with_fee_txs;

-- changeset harry:ST-94:20240828:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'primary_link' AND DATA_TYPE = 'text' AND TABLE_NAME = 'notifications' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE notifications MODIFY COLUMN primary_link TEXT NOT NULL;
ALTER TABLE notifications MODIFY COLUMN primary_text VARCHAR(512) NOT NULL;
ALTER TABLE notifications MODIFY COLUMN secondary_link TEXT NOT NULL;
ALTER TABLE notifications MODIFY COLUMN secondary_text VARCHAR(512) NOT NULL;
-- rollback ALTER TABLE notifications MODIFY COLUMN primary_link VARCHAR(255) NOT NULL;
-- rollback ALTER TABLE notifications MODIFY COLUMN primary_text VARCHAR(255) NOT NULL;
-- rollback ALTER TABLE notifications MODIFY COLUMN secondary_link VARCHAR(255) NOT NULL;
-- rollback ALTER TABLE notifications MODIFY COLUMN secondary_text VARCHAR(255) NOT NULL;

-- changeset harry:ST-108:20240829:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'app_version' AND DATA_TYPE = 'text' AND TABLE_NAME = 'users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE users MODIFY COLUMN app_version TEXT;
-- rollback ALTER TABLE users MODIFY COLUMN app_version VARCHAR(20);

-- changeset jason:KW-4047:20240822:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'usd_amount' and TABLE_NAME = 'assetpro_tx_logs' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE assetpro_tx_logs ADD usd_amount DECIMAL(16,4);
-- rollback ALTER TABLE assetpro_tx_logs DROP COLUMN usd_amount;

-- changeset jason:KW-4047:20240822:2
UPDATE assetpro_tx_logs SET usd_amount=amount WHERE usd_amount IS NULL;
-- rollback UPDATE assetpro_tx_logs SET usd_amount=NULL where usd_amount IS NOT NULL;

-- changeset jason:KW-4047:20240822:3
ALTER TABLE `asset_pro_product_base_infos` CHANGE COLUMN base_currency base_currency ENUM('USDT','USDC','ETH','MATIC','BNB','TRX') NOT NULL;
-- rollback ALTER TABLE `asset_pro_product_base_infos` CHANGE COLUMN base_currency base_currency ENUM('USDT','USDC') NOT NULL;

-- changeset jason:KW-4047:20240822:4
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos WHERE name IN ('ETH/ETH', 'MATIC/MATIC', 'BNB/BNB', 'TRX/TRON', 'ETH/ARB');
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image, quote_currency_logo) VALUES
('ETH/ETH', 'eth', 'ETH', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/eth.png','https://wallet-static.kryptogo.com/public/assets/images/eth_eth.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png'),
('MATIC/MATIC', 'matic', 'MATIC', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/matic_matic.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png'),
('BNB/BNB', 'bnb', 'BNB', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/******************************************.png','https://wallet-static.kryptogo.com/public/assets/images/bnb_bnb.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png'),
('TRX/TRON', 'tron', 'TRX', 'TWD', 'buy_crypto', 'https://static.tronscan.org/production/logo/trx.png','https://wallet-static.kryptogo.com/public/assets/images/trx_tron.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png'),
('ETH/ARB', 'arb', 'ETH', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/eth.png','https://wallet-static.kryptogo.com/public/assets/images/eth_arb.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('ETH/ETH', 'MATIC/MATIC', 'BNB/BNB', 'TRX/TRON', 'ETH/ARB');

-- changeset jason:KW-4047:20240822:5 context:dev
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM asset_pro_product_base_infos WHERE name IN ('ETH/SEPOLIA', 'TRX/SHASTA');
INSERT INTO asset_pro_product_base_infos (name, chain_id, base_currency, quote_currency, type, token_logo, image, quote_currency_logo) VALUES
('ETH/SEPOLIA', 'sepolia', 'ETH', 'TWD', 'buy_crypto', 'https://token-icons.s3.amazonaws.com/eth.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/eth_eth.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png'),
('TRX/SHASTA', 'shasta', 'TRX', 'TWD', 'buy_crypto', 'https://static.tronscan.org/production/logo/trx.png','https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/trx_tron.png', 'https://wallet-static.kryptogo.com/public/assets/images/currency_TWD.png');
-- rollback DELETE FROM asset_pro_product_base_infos WHERE name IN ('ETH/SEPOLIA', 'TRX/SHASTA');

-- changeset harry:KW-4066:20240903:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='nft_user_amounts' AND INDEX_NAME='idx_ua_chain_id_owner_address_amount_tx_timestamp';
CREATE INDEX idx_ua_chain_id_owner_address_amount_tx_timestamp ON nft_user_amounts (chain_id, owner_address, amount, tx_timestamp);
-- rollback DROP INDEX idx_ua_chain_id_owner_address_amount_tx_timestamp ON nft_user_amounts;

-- changeset harry:fix-locale-update:20240905:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='locale_map' AND CONSTRAINT_TYPE='PRIMARY KEY';
ALTER TABLE locale_map DROP PRIMARY KEY, ADD PRIMARY KEY (user_uid, client_id);
-- rollback ALTER TABLE locale_map DROP PRIMARY KEY, ADD PRIMARY KEY (user_uid);

-- changeset beans:add-index-wallets-address:20240905:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='wallets' AND INDEX_NAME='idx_address';
CREATE INDEX idx_address ON wallets(address);
-- rollback DROP INDEX idx_address ON wallets;

-- changeset beans:add-index-wallets-chain-address:20240905:2
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='wallets' AND INDEX_NAME='idx_chain_address';
CREATE INDEX idx_chain_address ON wallets(chain, address);
-- rollback DROP INDEX idx_chain_address ON wallets;

-- changeset raiven:KW-4058:20240911:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:2 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE (COLUMN_NAME IN ('status', 'deleted_at') AND TABLE_NAME = 'studio_users' AND TABLE_SCHEMA = '${MYSQL_DATABASE}');
UPDATE studio_users SET status = 'inactive', deleted_at = NULL WHERE deleted_at IS NOT NULL;
-- rollback UPDATE studio_users SET deleted_at = CURRENT_TIMESTAMP WHERE deleted_at IS NULL AND status = 'inactive';

-- changeset raiven:KW-4062:20240910:1
CREATE TABLE IF NOT EXISTS  `asset_pro_profit_rates` (
  `id` integer auto_increment,
  `organization_id` integer NOT NULL,
  `service` enum('buy','swap_gas','swap_defi','send_with_fee','send_gasless','send_batch') NOT NULL,
  `profit_rate` decimal(6,5) NOT NULL DEFAULT 0,
  `profit_share_ratio` decimal(6,5) NOT NULL DEFAULT 0,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_organization_id_service` (`organization_id`, `service`),
  FOREIGN KEY (`organization_id`) REFERENCES `studio_organizations` (`id`)
);
-- rollback DROP TABLE `asset_pro_profit_rates`;

-- changeset raiven:KW-4062:20240910:2 context:dev
INSERT INTO asset_pro_profit_rates (organization_id, service, profit_rate, profit_share_ratio)
VALUES
(1, 'buy', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'buy_crypto' LIMIT 1), 0),
(1 , 'swap_gas', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'gas_swap' LIMIT 1), 0),
(1, 'swap_defi', 0.5, 0),
(1, 'send_with_fee', 0.5, 0),
(1, 'send_gasless', 0.5, 0),
(1, 'send_batch', 0, 0),
(16, 'buy', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 16 AND liquidity_type = 'buy_crypto' LIMIT 1), 0),
(16, 'swap_gas', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 16 AND liquidity_type = 'gas_swap' LIMIT 1), 0.5),
(16, 'swap_defi', 0.008, 0.5),
(16, 'send_with_fee', 0.2, 0.05),
(16, 'send_gasless', 0.02, 0.05),
(16, 'send_batch', 0, 0);
-- rollback DELETE FROM asset_pro_profit_rates WHERE organization_id in (1, 16);

-- changeset raiven:KW-4062:20240910:2 context:staging
INSERT INTO asset_pro_profit_rates (organization_id, service, profit_rate, profit_share_ratio)
VALUES
(1, 'buy', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'buy_crypto' LIMIT 1), 0),
(1 , 'swap_gas', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'gas_swap' LIMIT 1), 0),
(1, 'swap_defi', 0.5, 0),
(1, 'send_with_fee', 0.5, 0),
(1, 'send_gasless', 0.5, 0),
(1, 'send_batch', 0, 0),
(8, 'buy', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 8 AND liquidity_type = 'buy_crypto' LIMIT 1), 0),
(8, 'swap_gas', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 8 AND liquidity_type = 'gas_swap' LIMIT 1), 0.5),
(8, 'swap_defi', 0.008, 0.5),
(8, 'send_with_fee', 0.2, 0.05),
(8, 'send_gasless', 0.02, 0.05),
(8, 'send_batch', 0, 0);
-- rollback DELETE FROM asset_pro_profit_rates WHERE organization_id in (1, 8);

-- changeset raiven:KW-4062:20240910:2 context:prod
INSERT INTO asset_pro_profit_rates (organization_id, service, profit_rate, profit_share_ratio)
VALUES
(1, 'buy', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'buy_crypto' LIMIT 1), 0),
(1 , 'swap_gas', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 1 AND liquidity_type = 'gas_swap' LIMIT 1), 0),
(1, 'swap_defi', 0.5, 0),
(1, 'send_with_fee', 0.5, 0),
(1, 'send_gasless', 0.5, 0),
(1, 'send_batch', 0, 0),
(16, 'buy', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 16 AND liquidity_type = 'buy_crypto' LIMIT 1), 0),
(16, 'swap_gas', (SELECT profit_margin_rate FROM asset_pro_liquidities WHERE organization_id = 16 AND liquidity_type = 'gas_swap' LIMIT 1), 0.5),
(16, 'swap_defi', 0.008, 0.5),
(16, 'send_with_fee', 0.2, 0.05),
(16, 'send_gasless', 0.02, 0.05),
(16, 'send_batch', 0, 0);
-- rollback DELETE FROM asset_pro_profit_rates WHERE organization_id in (1, 16);

-- changeset kw:KW-4062:20240910:3
-- preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='asset_pro_liquidities' AND COLUMN_NAME='profit_margin_rate' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE `asset_pro_liquidities` DROP COLUMN `profit_margin_rate`;
-- rollback ALTER TABLE `asset_pro_liquidities` ADD COLUMN `profit_margin_rate` decimal(6,5) NOT NULL;

-- changeset harry:ST-124:20240916:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='send_link_campaign_users' AND COLUMN_NAME='reward' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE send_link_campaign_users ADD reward DECIMAL(20,10);
ALTER TABLE send_link_campaign_users ADD reward_sent_at TIMESTAMP;
-- rollback ALTER TABLE send_link_campaign_users DROP COLUMN reward;
-- rollback ALTER TABLE send_link_campaign_users DROP COLUMN reward_sent_at;

-- changeset harry:fix-user-delete:20240923:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME='has_synced_assets' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
DROP TABLE has_synced_assets;
-- rollback CREATE TABLE has_synced_assets (user_uid VARCHAR(60) PRIMARY KEY, address VARCHAR(62) NOT NULL, created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, FOREIGN KEY (user_uid) REFERENCES users(uid));

-- changeset raiven:KW-4117:20240925:1
CREATE TABLE IF NOT EXISTS defi_swaps (
  id INTEGER AUTO_INCREMENT PRIMARY KEY,
  organization_id INTEGER NOT NULL,
  uid VARCHAR(60) NOT NULL,
  chain_id VARCHAR(20) NOT NULL,
  tx_hash VARCHAR(255) NOT NULL,
  fee_amount DECIMAL(22,9),
  token_address VARCHAR(42),
  token_price DECIMAL(22,9),
  profit_margin DECIMAL(22,9),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (organization_id) REFERENCES studio_organizations(id),
  INDEX idx_org_id (organization_id),
  INDEX idx_uid (uid),
  UNIQUE INDEX uniq_tx_hash (tx_hash)
);
-- rollback DROP TABLE defi_swaps;

-- changeset raiven:KW-4117:20240930:2
-- preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='defi_swaps' AND COLUMN_NAME IN ('amount', 'amount_usd') AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE defi_swaps
ADD COLUMN amount DECIMAL(22,9),
ADD COLUMN amount_usd DECIMAL(22,9);
-- rollback ALTER TABLE defi_swaps DROP COLUMN amount_usd, DROP COLUMN amount;

-- changeset harry:KW-4171:20241002:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='tx_updates' AND COLUMN_NAME='from_num' AND DATA_TYPE='int' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE tx_updates MODIFY COLUMN from_num INT UNSIGNED NOT NULL;
ALTER TABLE tx_updates MODIFY COLUMN to_num INT UNSIGNED NOT NULL;
ALTER TABLE tx_block_timestamp MODIFY COLUMN block_num INT UNSIGNED;
-- rollback ALTER TABLE tx_updates MODIFY COLUMN from_num INT NOT NULL;
-- rollback ALTER TABLE tx_updates MODIFY COLUMN to_num INT NOT NULL;
-- rollback ALTER TABLE tx_block_timestamp MODIFY COLUMN block_num INT;

-- changeset harry:fix-token-meta:20241015:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME='data_contract_metadata' AND INDEX_NAME='uniq_chain_contract' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
DELETE d1 FROM data_contract_metadata d1
  INNER JOIN data_contract_metadata d2
  WHERE d1.id < d2.id
    AND d1.chain_id = d2.chain_id
    AND d1.contract_address = d2.contract_address;
CREATE UNIQUE INDEX uniq_chain_contract ON data_contract_metadata (chain_id, contract_address);
-- rollback DROP INDEX uniq_chain_contract ON data_contract_metadata;

-- changeset harry:add-known-metadata:20241023:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='data_contract_metadata' AND COLUMN_NAME='icon' AND DATA_TYPE='varchar' AND CHARACTER_MAXIMUM_LENGTH=2084 AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE data_contract_metadata MODIFY COLUMN icon VARCHAR(2084) NOT NULL;
-- rollback ALTER TABLE data_contract_metadata MODIFY COLUMN icon VARCHAR(250) NOT NULL;

-- changeset harry:KW-4222:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA='${MYSQL_DATABASE}' AND TABLE_NAME='read_all_time_stamp_map' AND CONSTRAINT_TYPE='PRIMARY KEY';
ALTER TABLE read_all_time_stamp_map DROP PRIMARY KEY, ADD PRIMARY KEY (user_uid, client_id);
-- rollback ALTER TABLE read_all_time_stamp_map DROP PRIMARY KEY, ADD PRIMARY KEY (user_uid);

-- changeset harry:KW-4224:20241029:1
CREATE TABLE IF NOT EXISTS studio_organization_send_with_rents (
    id INTEGER AUTO_INCREMENT PRIMARY KEY,
    organization_id INTEGER NOT NULL,
    uid VARCHAR(60) NOT NULL,
    chain_id VARCHAR(20) NOT NULL,
    `from` VARCHAR(62) NOT NULL,
    recipient VARCHAR(62) NOT NULL,
    token_address VARCHAR(62) NOT NULL,
    amount VARCHAR(78) NOT NULL,
    fee DECIMAL(22,9) NOT NULL,
    signed_txs JSON NOT NULL,
    status ENUM('processing', 'success', 'failed') NOT NULL,
    energy_rent_cost DECIMAL(22,9),
    actual_cost_usd DECIMAL(22,9),
    token_transfer_tx_hash VARCHAR(88),
    fee_transfer_tx_hash VARCHAR(88),
    retry_count INTEGER NOT NULL DEFAULT 0,
    estimated_finish_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id),
    INDEX idx_org_id (organization_id),
    INDEX idx_uid (uid),
    INDEX idx_status (status)
);
-- rollback DROP TABLE studio_organization_send_with_rents;

-- changeset harry:ST-165:20241106:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME='studio_organization_send_with_rents' AND INDEX_NAME='idx_fee_transfer_tx_hash' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_fee_transfer_tx_hash ON studio_organization_send_with_rents (fee_transfer_tx_hash);
-- rollback DROP INDEX idx_fee_transfer_tx_hash ON studio_organization_send_with_rents;

-- changeset beans:increase-tx-details-decimal:20241112:1
ALTER TABLE tx_details MODIFY COLUMN value_decimal DECIMAL(36,18);
-- rollback ALTER TABLE tx_details MODIFY COLUMN value_decimal DECIMAL(20,8);

-- changeset raiven:KW-4252:20241112:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('profit_margin_rate', 'profit_share_ratio', 'profit_margin') and TABLE_NAME = 'studio_organization_send_with_rents' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_send_with_rents ADD profit_margin_rate decimal(6,5);
ALTER TABLE studio_organization_send_with_rents ADD profit_share_ratio decimal(3,2);
ALTER TABLE studio_organization_send_with_rents ADD profit_margin decimal(22,9);
-- rollback ALTER TABLE studio_organization_send_with_rents DROP COLUMN profit_margin_rate, DROP COLUMN profit_share_ratio, DROP COLUMN profit_margin;

-- changeset raiven:KW-4269:20241113:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('token_price', 'token_transfer_tx_hash', 'fee_transfer_tx_hash', 'profit_margin_rate', 'profit_share_ratio') and TABLE_NAME = 'studio_organization_gasless_sends' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gasless_sends ADD token_price DECIMAL(22,9);
ALTER TABLE studio_organization_gasless_sends ADD token_transfer_tx_hash VARCHAR(88);
ALTER TABLE studio_organization_gasless_sends ADD fee_transfer_tx_hash VARCHAR(88);
ALTER TABLE studio_organization_gasless_sends ADD profit_margin_rate decimal(6,5);
ALTER TABLE studio_organization_gasless_sends ADD profit_share_ratio decimal(3,2);
-- rollback ALTER TABLE studio_organization_gasless_sends  DROP COLUMN token_price, DROP COLUMN token_transfer_tx_hash, DROP COLUMN fee_transfer_tx_hash, DROP COLUMN profit_margin_rate, DROP COLUMN profit_share_ratio;

-- changeset raiven:KW-4269:20241113:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_NAME='studio_organization_gasless_sends' AND INDEX_NAME='idx_fee_transfer_tx_hash' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_fee_transfer_tx_hash ON studio_organization_gasless_sends (fee_transfer_tx_hash);
-- rollback DROP INDEX idx_fee_transfer_tx_hash ON studio_organization_gasless_sends;

-- changeset raiven:ST-183:20241203:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'binance_ticker' and TABLE_NAME = 'token_metadata' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE token_metadata ADD binance_ticker VARCHAR(100);
-- rollback ALTER TABLE token_metadata DROP COLUMN binance_ticker;

-- changeset beans:KW-4287:20241202:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME IN ('user_transfer_tx_hash', 'rent_cost', 'version') and TABLE_NAME = 'studio_organization_gas_swaps' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE studio_organization_gas_swaps 
  ADD COLUMN user_transfer_tx_hash varchar(88),
  ADD COLUMN rent_cost DECIMAL(22,9),
  ADD COLUMN version TINYINT NOT NULL DEFAULT 1;
-- rollback ALTER TABLE studio_organization_gas_swaps DROP COLUMN user_transfer_tx_hash, DROP COLUMN rent_cost, DROP COLUMN version;

-- changeset harry:KW-4352:20241211:1
CREATE TABLE IF NOT EXISTS payment_intents (
  id VARCHAR(32) PRIMARY KEY,
  payment_chain VARCHAR(20) NOT NULL,
  payment_address VARCHAR(60) NOT NULL,
  token_address VARCHAR(100) NOT NULL,
  symbol VARCHAR(100) NOT NULL,
  decimals SMALLINT UNSIGNED NOT NULL,
  crypto_amount DECIMAL(36,18) NOT NULL,
  fiat_amount DECIMAL(36,18) NOT NULL,
  fiat_currency VARCHAR(10) NOT NULL,
  payment_deadline TIMESTAMP NOT NULL,
  status ENUM('pending','success','expired') NOT NULL DEFAULT 'pending',
  payment_tx_hash VARCHAR(100),
  aggregation_tx_hash VARCHAR(100),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- rollback DROP TABLE payment_intents;

-- changeset harry:KW-4353:20241213:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT count(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'client_id' and TABLE_NAME = 'payment_intents' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE payment_intents ADD client_id VARCHAR(50) NOT NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN client_id;

-- changeset beans:KW-4355:20241216:1
--preconditions onFail:CONTINUE
--precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='payment_intents' AND index_name='idx_payment_chain_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE INDEX idx_payment_chain_address ON payment_intents(payment_chain, payment_address);
-- rollback DROP INDEX idx_payment_chain_address ON payment_intents;

-- changeset beans:KW-4355:20241226:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'payment_address_salt' and TABLE_NAME = 'payment_intents' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE payment_intents ADD COLUMN payment_address_salt VARCHAR(64) NOT NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN payment_address_salt;

-- changeset harry:ST-199:20241231:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'chain_addresses' and TABLE_NAME = 'contacts' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE contacts ADD COLUMN chain_addresses TEXT DEFAULT NULL;
-- rollback ALTER TABLE contacts DROP COLUMN chain_addresses;

-- changeset harry:fix-base-op:20250101:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'default_receive_wallets' and TABLE_NAME = 'users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE users ADD COLUMN default_receive_wallets TEXT DEFAULT NULL;
-- rollback ALTER TABLE users DROP COLUMN default_receive_wallets;

-- changeset beans:KW-4397:20250102:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = 'handle' AND TABLE_NAME = 'users' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE users ADD COLUMN handle VARCHAR(100) DEFAULT NULL;
CREATE UNIQUE INDEX uniq_users_handle ON users(handle);
-- rollback ALTER TABLE users DROP COLUMN handle;

-- changeset beans:KW-4937:20250106:1
-- preconditions onFail:CONTINUE
CREATE TABLE IF NOT EXISTS `studio_organization_bridge_records` (
  `id` int auto_increment,
  `organization_id` int NOT NULL,
  `uid` varchar(60),
  `from_chain_id` varchar(20) NOT NULL,
  `from_address` varchar(100) NOT NULL,
  `from_token_address` varchar(100) NOT NULL,
  `from_amount` decimal(36,18) NOT NULL,
  `from_tx_hash` varchar(100) NOT NULL,
  `to_chain_id` varchar(20) NOT NULL,
  `to_address` varchar(100) NOT NULL,
  `to_token_address` varchar(100) NOT NULL,
  `to_amount` decimal(36,18) NOT NULL,
  `to_tx_hash` varchar(100),
  `fee_chain_id` varchar(20) NOT NULL,
  `fee_receive_address` varchar(100) NOT NULL,
  `fee_token_address` varchar(100) NOT NULL,
  `fee_amount` decimal(36,18) NOT NULL,
  `fee_tx_hash` varchar(100) NOT NULL,
  `fee_token_price` decimal(22,9) NOT NULL,
  `fee_usd` decimal(22,9) NOT NULL,
  `profit_kg_minimum_rate` decimal(6,5) NOT NULL,
  `profit_rate` decimal(6,5) NOT NULL,
  `profit_share_ratio` decimal(6,5) NOT NULL,
  `profit_margin` decimal(22,9) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`organization_id`) REFERENCES studio_organizations(id),
  FOREIGN KEY (`uid`) REFERENCES users(uid),
  INDEX `idx_uid` (`uid`),
  INDEX `idx_org_id` (`organization_id`),
  UNIQUE INDEX `uniq_from_tx_hash` (`from_tx_hash`),
  UNIQUE INDEX `uniq_to_tx_hash` (`to_tx_hash`)
);
-- rollback DROP TABLE studio_organization_bridge_records;

-- changeset beans:KW-4937:20250108
ALTER TABLE asset_pro_profit_rates MODIFY COLUMN service ENUM('buy','swap_gas','swap_defi','send_with_fee','send_gasless','send_batch', 'bridge') NOT NULL;
-- rollback ALTER TABLE asset_pro_profit_rates MODIFY COLUMN service ENUM('buy','swap_gas','swap_defi','send_with_fee','send_gasless','send_batch') NOT NULL;

-- changeset beans:KW-4414:20250109:1
ALTER TABLE payment_intents ADD COLUMN callback_url varchar(1000) DEFAULT NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN callback_url;

-- changeset harry:ST-206:20250109:1
ALTER TABLE studio_organization_wallets MODIFY COLUMN wallet_type ENUM('evm', 'tron', 'sol') NOT NULL;
-- rollback ALTER TABLE studio_organization_wallets MODIFY COLUMN wallet_type ENUM('evm', 'tron') NOT NULL;

-- changeset harry:KW-4937:20250108:2
CREATE TABLE universal_swaps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uid VARCHAR(60) NOT NULL,
    status ENUM('pending', 'success', 'failed') NOT NULL,
    fee_rate DECIMAL(10,6) NOT NULL,
    estimated_finish_at TIMESTAMP NOT NULL,
    retry_count INT NOT NULL DEFAULT 0,
    dest_chain VARCHAR(20) NOT NULL,
    dest_wallet_address VARCHAR(62) NOT NULL,
    dest_token_id VARCHAR(62) NOT NULL,
    dest_received_raw_amount VARCHAR(78),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_uid (uid),
    INDEX idx_status (status)
);

CREATE TABLE universal_swap_source_txs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    swap_id INT NOT NULL,
    chain VARCHAR(20) NOT NULL,
    `from` VARCHAR(62) NOT NULL,
    `to` VARCHAR(62) NOT NULL,
    token_id VARCHAR(62) NOT NULL,
    raw_amount VARCHAR(78) NOT NULL,
    signed_tx TEXT NOT NULL,
    tx_hash VARCHAR(88),
    status ENUM('init', 'broadcasted', 'confirmed', 'failed') NOT NULL,
    funds_sent BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_swap_id (swap_id),
    FOREIGN KEY (swap_id) REFERENCES universal_swaps(id) ON DELETE CASCADE
);

CREATE TABLE universal_swap_sponsor_txs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    swap_id INT NOT NULL,
    chain VARCHAR(20) NOT NULL,
    `from` VARCHAR(62) NOT NULL,
    `to` VARCHAR(62) NOT NULL,
    raw_amount VARCHAR(78) NOT NULL,
    tx_hash VARCHAR(88),
    status ENUM('init', 'broadcasted', 'confirmed', 'failed') NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_swap_id (swap_id),
    FOREIGN KEY (swap_id) REFERENCES universal_swaps(id) ON DELETE CASCADE
);
-- rollback DROP TABLE universal_swap_sponsor_txs;
-- rollback DROP TABLE universal_swap_source_txs;
-- rollback DROP TABLE universal_swaps;

-- changeset beans:KW-4414:20250115:1
ALTER TABLE payment_intents ADD COLUMN order_data JSON;
-- rollback ALTER TABLE payment_intents DROP COLUMN order_data;

-- changeset harry:fix-bridge-migration:20250121:1 context:dev
INSERT INTO asset_pro_profit_rates (organization_id, service, profit_rate, profit_share_ratio)
VALUES (1, 'bridge', 0.005, 0.1), (16, 'bridge', 0.005, 0.1);
-- rollback DELETE FROM asset_pro_profit_rates WHERE organization_id in (1, 16) and service = 'bridge';

-- changeset harry:fix-bridge-migration:20250121:2 context:staging
INSERT INTO asset_pro_profit_rates (organization_id, service, profit_rate, profit_share_ratio)
VALUES (1, 'bridge', 0.005, 0.1), (8, 'bridge', 0.005, 0.1);
-- rollback DELETE FROM asset_pro_profit_rates WHERE organization_id in (1, 8) and service = 'bridge';

-- changeset harry:fix-bridge-migration:20250121:3 context:prod
INSERT INTO asset_pro_profit_rates (organization_id, service, profit_rate, profit_share_ratio)
VALUES (1, 'bridge', 0.005, 0.1), (16, 'bridge', 0.005, 0.1);
-- rollback DELETE FROM asset_pro_profit_rates WHERE organization_id in (1, 16) and service = 'bridge';

-- changeset harry:ST-233:20250206:1
ALTER TABLE users ADD COLUMN tx_referral_code VARCHAR(20) DEFAULT NULL UNIQUE;
-- rollback ALTER TABLE users DROP COLUMN tx_referral_code;

-- changeset harry:ST-233:20250211:1
CREATE TABLE referral_rewards (
    id INT AUTO_INCREMENT PRIMARY KEY,
    referrer_id VARCHAR(60) NOT NULL REFERENCES users(uid),
    from_address VARCHAR(62) NOT NULL,
    tx_hash VARCHAR(88) NOT NULL,
    amount VARCHAR(78) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    UNIQUE KEY (tx_hash)
);
CREATE INDEX idx_referral_rewards_referrer_id ON referral_rewards(referrer_id);

CREATE TABLE referral_balances (
    user_id VARCHAR(60) NOT NULL PRIMARY KEY REFERENCES users(uid),
    total_rewards VARCHAR(78) NOT NULL,
    available_rewards VARCHAR(78) NOT NULL,
    withdrawn_rewards VARCHAR(78) NOT NULL,
    updated_at TIMESTAMP NOT NULL
);

CREATE TABLE referral_withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(60) NOT NULL REFERENCES users(uid),
    amount VARCHAR(78) NOT NULL,
    recipient_address VARCHAR(62) NOT NULL,
    tx_hash VARCHAR(88),
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
CREATE INDEX idx_referral_withdrawals_user_id ON referral_withdrawals(user_id);
-- rollback DROP TABLE IF EXISTS referral_withdrawals;
-- rollback DROP TABLE IF EXISTS referral_balances;
-- rollback DROP TABLE IF EXISTS referral_rewards;

-- changeset beans:KW-4498:20250312:1
CREATE TABLE IF NOT EXISTS `studio_user_api_keys` (
  `id` INT AUTO_INCREMENT NOT NULL,
  `uid` VARCHAR(60) NOT NULL,
  `org_id` INT NOT NULL,
  `name` VARCHAR(250) NOT NULL,
  `key_prefix` VARCHAR(10) NOT NULL,
  `key_hash` VARCHAR(64) NOT NULL,
  `description` VARCHAR(500) NULL,
  `last_used_at` TIMESTAMP NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` TIMESTAMP NULL,
  CONSTRAINT `studio_user_api_keys_PK` PRIMARY KEY (`id`),
  UNIQUE (`key_hash`),
  INDEX `idx_studio_user_api_keys_uid` (`uid`),
  INDEX `idx_studio_user_api_keys_org_id` (`org_id`)
);
-- rollback DROP TABLE studio_user_api_keys;

-- changeset harry:ST-310:20250313:1
-- Token Buy Signals Table
CREATE TABLE IF NOT EXISTS token_buy_signals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  token_address VARCHAR(44) NOT NULL,
  smart_wallet_count INT NOT NULL,
  buy_entry_price DECIMAL(20, 8) NOT NULL,
  emit_time TIMESTAMP NOT NULL,
  telegram_link VARCHAR(255) NOT NULL,
  win_rate DECIMAL(5, 2) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_token_address (token_address)
);
CREATE TABLE IF NOT EXISTS token_sell_signals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  token_address VARCHAR(44) NOT NULL,
  emit_time TIMESTAMP NOT NULL,
  telegram_link VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_token_address (token_address),
  INDEX idx_emit_time (emit_time)
);
-- rollback DROP TABLE IF EXISTS token_buy_signals;
-- rollback DROP TABLE IF EXISTS token_sell_signals;

-- changeset harry:ST-319:20250317:1
ALTER TABLE token_buy_signals
ADD COLUMN average_holding DECIMAL(20, 8) NOT NULL DEFAULT 0,
ADD COLUMN average_win_rate DECIMAL(5, 2) NOT NULL DEFAULT 0;
-- rollback ALTER TABLE token_buy_signals DROP COLUMN average_holding, DROP COLUMN average_win_rate;

-- changeset beans:KW-4498:20250318:1
ALTER TABLE payment_intents ADD COLUMN org_id INT NOT NULL DEFAULT 0;
-- rollback ALTER TABLE payment_intents DROP COLUMN org_id;

-- changeset beans:KW-4498:20250318:2
-- Update existing payment_intents records with the correct org_id from studio_organization_clients
UPDATE payment_intents pi
JOIN studio_organization_clients soc ON pi.client_id = soc.client_id
SET pi.org_id = soc.organization_id
WHERE pi.org_id = 0;
-- rollback UPDATE payment_intents SET org_id = 0;

-- changeset harry:ST-326:20250321:1
ALTER TABLE token_buy_signals
ADD COLUMN highest_price DECIMAL(20, 8) NOT NULL DEFAULT 0;
-- rollback ALTER TABLE token_buy_signals DROP COLUMN highest_price;

-- changeset beans:add-insufficient-status:20250324:1
ALTER TABLE payment_intents 
MODIFY COLUMN status ENUM('pending','success','expired','insufficient_refunded','insufficient_not_refunded') NOT NULL DEFAULT 'pending';
-- rollback ALTER TABLE payment_intents MODIFY COLUMN status ENUM('pending','success','expired') NOT NULL DEFAULT 'pending';

-- changeset beans:add-insufficient-status:20250324:2
ALTER TABLE payment_intents
ADD COLUMN received_amount DECIMAL(36,18) NULL,
ADD COLUMN aggregated_amount DECIMAL(36,18) NULL,
ADD COLUMN refund_amount DECIMAL(36,18) NULL,
ADD COLUMN refund_tx_hash VARCHAR(100) NULL,
ADD COLUMN kg_fee_amount DECIMAL(36,18) NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN refund_tx_hash, DROP COLUMN received_amount, DROP COLUMN aggregated_amount, DROP COLUMN refund_amount, DROP COLUMN kg_fee_amount;

-- changeset beans:add-scenario-id:20250326:1
ALTER TABLE payment_intents
ADD COLUMN scenario_id VARCHAR(100) NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN scenario_id;

-- changeset beans:rename-scenario-id-to-group-key:20250327:1
ALTER TABLE payment_intents
CHANGE COLUMN scenario_id group_key VARCHAR(100) NULL;
-- rollback ALTER TABLE payment_intents CHANGE COLUMN group_key scenario_id VARCHAR(100) NULL;

-- changeset beans:add-payment-items:20250411:1
CREATE TABLE IF NOT EXISTS payment_items (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(36,18) NOT NULL,
  currency VARCHAR(10) NOT NULL,
  image VARCHAR(1000),
  success_url VARCHAR(1000),
  error_url VARCHAR(1000),
  organization_id INT NOT NULL,
  order_data_fields JSON NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
-- rollback DROP TABLE payment_items;

-- changeset beans:add-payment-items-index:20250404:2
CREATE INDEX idx_payment_items_organization_id ON payment_items(organization_id);
-- rollback DROP INDEX idx_payment_items_organization_id ON payment_items;

-- changeset beans:add-config-to-payment-items:20250415:1
ALTER TABLE payment_items
ADD COLUMN config JSON NULL;
-- rollback ALTER TABLE payment_items DROP COLUMN config;

-- changeset beans:add-client-id-to-payment-items:20250417:1
ALTER TABLE payment_items
ADD COLUMN client_id VARCHAR(36) NOT NULL;
-- rollback ALTER TABLE payment_items DROP COLUMN client_id;

-- changeset beans:add-client-id-index:20250417:2
CREATE INDEX idx_payment_items_client_id ON payment_items(client_id);
-- rollback DROP INDEX idx_payment_items_client_id ON payment_items;

-- changeset beans:populate-client-id:20250417:3
-- For each payment_item, find a client from the same organization and use its client_id
-- This assumes each organization has at least one client
UPDATE payment_items pi
JOIN (
    SELECT 
        soc.organization_id, 
        MIN(soc.client_id) as default_client_id
    FROM 
        studio_organization_clients soc
    GROUP BY 
        soc.organization_id
) org_clients ON pi.organization_id = org_clients.organization_id
SET 
    pi.client_id = org_clients.default_client_id
WHERE 
    pi.client_id = '';
-- rollback UPDATE payment_items SET client_id = '';

-- changeset beans:add-payer-address-to-payment-intents:20250418:1
ALTER TABLE payment_intents
ADD COLUMN payer_address VARCHAR(60) NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN payer_address;

-- changeset beans:add-payment-tx-timestamp-to-payment-intents:20250418:2
ALTER TABLE payment_intents
ADD COLUMN payment_tx_timestamp TIMESTAMP NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN payment_tx_timestamp;

-- changeset beans:add-pricing-mode-to-payment-intents:20250418:3
ALTER TABLE payment_intents
ADD COLUMN pricing_mode ENUM('fiat', 'crypto') NOT NULL DEFAULT 'fiat';
-- rollback ALTER TABLE payment_intents DROP COLUMN pricing_mode;

-- changeset beans:make-fiat-fields-nullable:20250419:1
ALTER TABLE payment_intents
MODIFY COLUMN fiat_amount DECIMAL(36,18) NULL,
MODIFY COLUMN fiat_currency VARCHAR(10) NULL;
-- rollback ALTER TABLE payment_intents MODIFY COLUMN fiat_amount DECIMAL(36,18) NOT NULL, MODIFY COLUMN fiat_currency VARCHAR(10) NOT NULL;

-- changeset harry:ST-381:20250422:1
ALTER TABLE token_sell_signals
ADD COLUMN highest_gain DECIMAL(20, 8) NOT NULL DEFAULT 0;
-- rollback ALTER TABLE token_sell_signals DROP COLUMN highest_gain;

-- changeset beans:add-unique-index-payment-items-name-org-client:20250424:1
CREATE UNIQUE INDEX idx_payment_items_name_org_client ON payment_items(name, organization_id, client_id);
-- rollback DROP INDEX idx_payment_items_name_org_client ON payment_items;

-- changeset beans:create-org-free-send-count-table:20250425:1
CREATE TABLE `org_free_send_count` (
  `id` int NOT NULL AUTO_INCREMENT,
  `organization_id` int NOT NULL,
  `used_count` int NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `organization_id` (`organization_id`),
  CONSTRAINT `fk_org_free_send_count_organization_id` FOREIGN KEY (`organization_id`) REFERENCES `studio_organizations` (`id`)
);
-- rollback DROP TABLE `org_free_send_count`;

-- changeset beans:add-buy-entry-time-to-token-sell-signals:20250502:1
ALTER TABLE token_sell_signals
ADD COLUMN buy_entry_time TIMESTAMP NULL;
-- rollback ALTER TABLE token_sell_signals DROP COLUMN buy_entry_time;

-- changeset harry:ST-406-drop-index:20250505:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='token_sell_signals' AND index_name='idx_token_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
ALTER TABLE token_sell_signals DROP INDEX idx_token_address;
-- rollback CREATE INDEX idx_token_address ON token_sell_signals(token_address);

-- changeset harry:ST-406-create-unique-index:20250505:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE table_name='token_sell_signals' AND index_name='idx_token_sell_signals_token_address' AND TABLE_SCHEMA='${MYSQL_DATABASE}';
CREATE UNIQUE INDEX idx_token_sell_signals_token_address ON token_sell_signals(token_address);
-- rollback DROP INDEX idx_token_sell_signals_token_address ON token_sell_signals;

-- changeset beans:add-payment-intent-stats:20250505:1
CREATE TABLE payment_intent_stats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  organization_id INT UNIQUE NOT NULL,
  total_revenue JSON NOT NULL,
  valid_order_count INT NOT NULL,
  unique_customer_count INT NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL
);
-- rollback DROP TABLE payment_intent_stats;

-- changeset beans:KW-4617_studio_wallet_import:20250506:1
-- preconditions onFail:WARN
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'studio_organization_imported_addresses';
CREATE TABLE studio_organization_imported_addresses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    organization_id INT NOT NULL,
    chain VARCHAR(10) NOT NULL,
    address VARCHAR(255) NOT NULL,
    added_by_user_id VARCHAR(60) NOT NULL,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (added_by_user_id) REFERENCES users(uid),
    INDEX idx_org_id_chain (organization_id, chain),
    UNIQUE INDEX uniq_org_id_chain_address (organization_id, chain, address)
);
-- rollback DROP TABLE IF EXISTS studio_organization_imported_addresses;

-- changeset harry:ST-418:20250513:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'token_analysis_credits';
CREATE TABLE token_analysis_credits (
    wallet_address VARCHAR(62) PRIMARY KEY,
    credits INT NOT NULL DEFAULT 0,
    updated_at TIMESTAMP NOT NULL
);
-- rollback DROP TABLE IF EXISTS token_analysis_credits;

-- changeset harry:ST-418:20250513:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'token_analysis_trading_volumes';
CREATE TABLE token_analysis_trading_volumes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    wallet_address VARCHAR(62) NOT NULL,
    tx_hash VARCHAR(88) NOT NULL,
    volume_usd DECIMAL(20,8) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    INDEX idx_wallet_address (wallet_address),
    UNIQUE INDEX idx_tx_hash (tx_hash)
);
-- rollback DROP TABLE IF EXISTS token_analysis_trading_volumes;

-- changeset harry:ST-418:20250513:3
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'token_analyses';
CREATE TABLE token_analyses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    wallet_address VARCHAR(62) NOT NULL,
    token_address VARCHAR(62) NOT NULL,
    analysis JSON NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    INDEX idx_wallet_address (wallet_address)
);
-- rollback DROP TABLE IF EXISTS token_analyses;

-- changeset beans:add-price-to-payment-intents:20250514:1
ALTER TABLE payment_intents
ADD COLUMN crypto_price DECIMAL(20,8) NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN crypto_price, DROP COLUMN fiat_price;

-- changeset beans:update-existing-payment-intents-price:20250514:2
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:1 SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'payment_intents' AND COLUMN_NAME = 'crypto_price';
UPDATE payment_intents SET crypto_price = 1.0 WHERE crypto_price IS NULL;
-- rollback UPDATE payment_intents SET crypto_price = NULL WHERE crypto_price = 1.0;

-- changeset beans:default-receive-address-of-studio-organization:20250514:1
ALTER TABLE studio_organization_imported_addresses 
ADD COLUMN default_receive_address BOOLEAN NOT NULL DEFAULT FALSE;
-- rollback ALTER TABLE studio_organization_imported_addresses DROP COLUMN default_receive_address;

-- changeset beans:add-permit-data-to-source-transactions:20250520:1
ALTER TABLE universal_swap_source_txs ADD COLUMN permit_data TEXT; 
-- rollback ALTER TABLE universal_swap_source_txs DROP COLUMN permit_data;

-- changeset beans:add-timestamps-to-payment-intents:20250526:1
ALTER TABLE payment_intents
ADD COLUMN aggregation_tx_timestamp TIMESTAMP NULL,
ADD COLUMN finalized_timestamp TIMESTAMP NULL;
-- rollback ALTER TABLE payment_intents DROP COLUMN aggregation_tx_timestamp, DROP COLUMN finalized_timestamp;

-- changeset beans:add-callback-url-to-payment-items:20250527:1
ALTER TABLE payment_items
ADD COLUMN callback_url VARCHAR(1000) NULL;
-- rollback ALTER TABLE payment_items DROP COLUMN callback_url;

-- changeset beans:add-callback-logs-table:20250530:1
CREATE TABLE IF NOT EXISTS callback_logs (
    id VARCHAR(36) PRIMARY KEY,
    payment_intent_id VARCHAR(36) NULL,
    url VARCHAR(1000) NOT NULL,
    type ENUM('payment', 'test') NOT NULL,
    status ENUM('success', 'failed') NOT NULL,
    status_code SMALLINT NULL,
    callback_payload TEXT NOT NULL,
    error TEXT NULL,
    duration_ms INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
    INDEX idx_payment_intent_id (payment_intent_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
-- rollback DROP TABLE callback_logs;

-- changeset beans:add-pay-token-to-payment-items:20250604:1
ALTER TABLE payment_items
ADD COLUMN pay_token VARCHAR(100) NULL;
-- rollback ALTER TABLE payment_items DROP COLUMN pay_token;
-- changeset paul:ST-447-token-analysis-credit-purchases:20250602:1
-- preconditions onFail:CONTINUE
-- precondition-sql-check expectedResult:0 SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '${MYSQL_DATABASE}' AND TABLE_NAME = 'token_analysis_credit_purchases';
CREATE TABLE token_analysis_credit_purchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    wallet_address VARCHAR(62) NOT NULL,
    sol_amount DECIMAL(20,8) NOT NULL,
    credits_purchased INT NOT NULL,
    tx_hash VARCHAR(88) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE INDEX idx_tx_hash (tx_hash),
    INDEX idx_wallet_address (wallet_address),
    INDEX idx_created_at (created_at)
);
-- rollback DROP TABLE IF EXISTS token_analysis_credit_purchases;

-- changeset beans:add-client-org-to-callback-logs:20250610:1
ALTER TABLE callback_logs
ADD COLUMN client_id VARCHAR(255) NULL,
ADD COLUMN org_id INT NULL,
ADD INDEX idx_client_id (client_id),
ADD INDEX idx_org_id (org_id),
ADD INDEX idx_org_client_id (org_id, client_id);
-- rollback ALTER TABLE callback_logs DROP COLUMN client_id, DROP COLUMN org_id, DROP INDEX idx_client_id, DROP INDEX idx_org_id, DROP INDEX idx_org_client_id;

-- changeset dorara:add-payment-item-success-message:20250624:1
ALTER TABLE payment_items
ADD COLUMN success_message TEXT NULL;
-- rollback ALTER TABLE payment_items DROP COLUMN success_message;

-- changeset dorara:add-payment-item-chain-id:20250708:1
ALTER TABLE payment_items
ADD COLUMN chain_id VARCHAR(100) NULL;
-- rollback ALTER TABLE payment_items DROP COLUMN chain_id;

-- changeset dorara:KW-4760:20250728:1
ALTER TABLE studio_organizations
ADD COLUMN business_type ENUM('individual', 'enterprise') NULL;
-- rollback ALTER TABLE studio_organizations DROP COLUMN business_type;
-- changeset dorara:create bridge-organization-table:********:1
CREATE TABLE bridge_organizations (
    organization_id INT PRIMARY KEY,
    customer_id VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    type ENUM('individual', 'business') NOT NULL,
    kyc_link TEXT,
    tos_link TEXT,
    kyc_status ENUM('not_started', 'incomplete', 'awaiting_ubo', 'under_review', 'approved', 'rejected', 'paused', 'offboarded') DEFAULT 'not_started',
    tos_status ENUM('pending', 'approved') DEFAULT 'pending',
    rejection_reasons JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE
);

CREATE TABLE bridge_external_accounts (
    bridge_external_account_id VARCHAR(255) PRIMARY KEY,
    organization_id INT NOT NULL,
    bank_name VARCHAR(255) NOT NULL,
    account_number VARCHAR(255) NOT NULL,
    account_type VARCHAR(255) NOT NULL,
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE
);

CREATE TABLE bridge_transfers (
    bridge_transfer_id VARCHAR(255) PRIMARY KEY,
    organization_id INT NOT NULL,
    bridge_external_account_id VARCHAR(255),
    chain VARCHAR(50) NOT NULL,
    from_address VARCHAR(255) NOT NULL,
    amount VARCHAR(255) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    status ENUM('awaiting_funds', 'in_review', 'funds_received', 'payment_submitted', 'payment_processed', 'canceled', 'error', 'undeliverable', 'returned', 'refunded') NOT NULL,
    deposit_to_address VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uniq_bridge_transfer_id (bridge_transfer_id),
    FOREIGN KEY (organization_id) REFERENCES studio_organizations(id) ON DELETE CASCADE,
    INDEX idx_organization_id (organization_id)
);
-- rollback DROP TABLE bridge_organizations;


-- changeset system:add-payout-target-address-to-payment-intents:********:1
ALTER TABLE payment_intents
ADD COLUMN payout_target_address VARCHAR(60);
-- rollback ALTER TABLE payment_intents DROP COLUMN payout_target_address;

-- changeset system:add-cancelled-status-to-payment-intents:20250801:1
-- Add 'cancelled' status to payment_intents table to support cancel intent functionality
ALTER TABLE payment_intents
MODIFY COLUMN status ENUM('pending','success','expired','insufficient_refunded','insufficient_not_refunded','cancelled') NOT NULL DEFAULT 'pending';
-- rollback ALTER TABLE payment_intents MODIFY COLUMN status ENUM('pending','success','expired','insufficient_refunded','insufficient_not_refunded') NOT NULL DEFAULT 'pending';
