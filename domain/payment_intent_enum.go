// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// PaymentIntentStatusPending is a PaymentIntentStatus of type pending.
	PaymentIntentStatusPending PaymentIntentStatus = "pending"
	// PaymentIntentStatusSuccess is a PaymentIntentStatus of type success.
	PaymentIntentStatusSuccess PaymentIntentStatus = "success"
	// PaymentIntentStatusExpired is a PaymentIntentStatus of type expired.
	PaymentIntentStatusExpired PaymentIntentStatus = "expired"
	// PaymentIntentStatusInsufficientRefunded is a PaymentIntentStatus of type insufficient_refunded.
	PaymentIntentStatusInsufficientRefunded PaymentIntentStatus = "insufficient_refunded"
	// PaymentIntentStatusInsufficientNotRefunded is a PaymentIntentStatus of type insufficient_not_refunded.
	PaymentIntentStatusInsufficientNotRefunded PaymentIntentStatus = "insufficient_not_refunded"
	// PaymentIntentStatusCancelled is a PaymentIntentStatus of type cancelled.
	PaymentIntentStatusCancelled PaymentIntentStatus = "cancelled"
)

var ErrInvalidPaymentIntentStatus = errors.New("not a valid PaymentIntentStatus")

// String implements the Stringer interface.
func (x PaymentIntentStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x PaymentIntentStatus) IsValid() bool {
	_, err := ParsePaymentIntentStatus(string(x))
	return err == nil
}

var _PaymentIntentStatusValue = map[string]PaymentIntentStatus{
	"pending":                   PaymentIntentStatusPending,
	"success":                   PaymentIntentStatusSuccess,
	"expired":                   PaymentIntentStatusExpired,
	"insufficient_refunded":     PaymentIntentStatusInsufficientRefunded,
	"insufficient_not_refunded": PaymentIntentStatusInsufficientNotRefunded,
	"cancelled":                 PaymentIntentStatusCancelled,
}

// ParsePaymentIntentStatus attempts to convert a string to a PaymentIntentStatus.
func ParsePaymentIntentStatus(name string) (PaymentIntentStatus, error) {
	if x, ok := _PaymentIntentStatusValue[name]; ok {
		return x, nil
	}
	return PaymentIntentStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidPaymentIntentStatus)
}
