//go:generate go-enum
package domain

import (
	"time"

	"github.com/shopspring/decimal"
)

// ENUM(pending, success, expired, insufficient_refunded, insufficient_not_refunded, cancelled)
type PaymentIntentStatus string

type PaymentIntent struct {
	ID                     string
	ClientID               string
	OrgID                  int
	PaymentChain           Chain
	PaymentAddress         Address
	PayerAddress           Address
	PayoutTargetAddress    Address
	PaymentAddressSalt     string
	TokenAddress           string
	Symbol                 string
	Decimals               uint
	CryptoAmount           decimal.Decimal
	ReceivedCryptoAmount   *decimal.Decimal
	AggregatedCryptoAmount *decimal.Decimal
	RefundCryptoAmount     *decimal.Decimal
	KGFeeAmount            *decimal.Decimal
	FiatAmount             *decimal.Decimal // Only used in fiat pricing mode
	FiatCurrency           *string          // Only used in fiat pricing mode
	CryptoPrice            *decimal.Decimal // Price of one unit of the crypto asset in terms of the fiat currency
	PricingMode            string           // "fiat" or "crypto"
	PaymentDeadline        time.Time
	Status                 PaymentIntentStatus
	PaymentTxHash          *string
	PaymentTxTimestamp     *time.Time
	AggregationTxHash      *string
	AggregationTxTimestamp *time.Time
	FinalizedTimestamp     *time.Time
	RefundTxHash           *string
	CancelledAt            *time.Time
	OrderData              map[string]any
	CallbackURL            *string
	GroupKey               *string
}

type PaymentIntentUpdate struct {
	Status                 *PaymentIntentStatus
	PaymentTxHash          *string
	PaymentTxTimestamp     *time.Time
	AggregationTxHash      *string
	AggregationTxTimestamp *time.Time
	FinalizedTimestamp     *time.Time
	ReceivedCryptoAmount   *decimal.Decimal
	AggregatedCryptoAmount *decimal.Decimal
	RefundCryptoAmount     *decimal.Decimal
	RefundTxHash           *string
	KGFeeAmount            *decimal.Decimal
	PayerAddress           *Address
	CancelledAt            *time.Time
}
