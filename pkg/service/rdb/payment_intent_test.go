package rdb

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Helper function to create a test payment intent with fiat mode (backward compatibility)
func createTestPaymentIntent(t *testing.T) *domain.PaymentIntent {
	return createTestPaymentIntentWithMode(t, "fiat")
}

// Helper function to create a test payment intent with specific pricing mode
func createTestPaymentIntentWithMode(t *testing.T, pricingMode string) *domain.PaymentIntent {
	ctx := context.Background()
	repo := GormRepo()
	now := time.Now().UTC()

	evmAddr := domain.NewEvmAddress("******************************************")
	var payoutAddr domain.Address = evmAddr
	intent := &domain.PaymentIntent{
		PaymentChain:        domain.Arbitrum,
		PaymentAddress:      domain.NewEvmAddress("******************************************"),
		PayoutTargetAddress: payoutAddr,
		PaymentAddressSalt:  "a7c8e91a33e26fe25c25aefdd0918343020b5db070d1c1e085543a3c6c0fe48a",
		TokenAddress:        "******************************************",
		Symbol:              "USDT",
		Decimals:            6,
		CryptoAmount:        decimal.NewFromFloat(100),
		PricingMode:         pricingMode,
		PaymentDeadline:     now.Add(30 * time.Minute),
		Status:              domain.PaymentIntentStatusPending,
		OrderData:           map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(100)},
		CallbackURL:         util.Ptr("https://example.com/callback"),
	}

	// Set FiatAmount and FiatCurrency only for fiat mode
	if pricingMode == "fiat" {
		fiatAmount := decimal.NewFromFloat(100)
		fiatCurrency := "USD"
		intent.FiatAmount = &fiatAmount
		intent.FiatCurrency = &fiatCurrency
	}

	created, err := repo.CreatePaymentIntent(ctx, intent)
	require.NoError(t, err)
	assert.NotEmpty(t, created.ID)
	return created
}

func TestCreatePaymentIntent(t *testing.T) {
	Reset()

	t.Run("CreatePaymentIntent_FiatMode", func(t *testing.T) {
		intent := createTestPaymentIntentWithMode(t, "fiat")
		assert.Equal(t, "fiat", intent.PricingMode)
		assert.NotNil(t, intent.FiatAmount)
		assert.NotNil(t, intent.FiatCurrency)
		assert.Equal(t, "100", intent.FiatAmount.String())
		assert.Equal(t, "USD", *intent.FiatCurrency)
	})

	t.Run("CreatePaymentIntent_CryptoMode", func(t *testing.T) {
		intent := createTestPaymentIntentWithMode(t, "crypto")
		assert.Equal(t, "crypto", intent.PricingMode)
		assert.Nil(t, intent.FiatAmount)
		assert.Nil(t, intent.FiatCurrency)
	})

	t.Run("CreatePaymentIntent_InvalidOrderData", func(t *testing.T) {
		ctx := context.Background()
		repo := GormRepo()
		now := time.Now().UTC()

		// Create a payment intent with fiat mode
		fiatAmount := decimal.NewFromFloat(100)
		fiatCurrency := "USD"

		evmAddr := domain.NewEvmAddress("******************************************")
		var payoutAddr domain.Address = evmAddr
		intent := &domain.PaymentIntent{
			PaymentChain:        domain.Arbitrum,
			PaymentAddress:      domain.NewEvmAddress("******************************************"),
			PayoutTargetAddress: payoutAddr,
			PaymentAddressSalt:  "a7c8e91a33e26fe25c25aefdd0918343020b5db070d1c1e085543a3c6c0fe48a",
			TokenAddress:        "******************************************",
			Symbol:              "USDT",
			Decimals:            6,
			CryptoAmount:        decimal.NewFromFloat(100),
			FiatAmount:          &fiatAmount,
			FiatCurrency:        &fiatCurrency,
			PricingMode:         "fiat",
			PaymentDeadline:     now.Add(30 * time.Minute),
			Status:              domain.PaymentIntentStatusPending,
			CallbackURL:         util.Ptr("https://example.com/callback"),
		}

		// generate a string with 2000 characters
		longString := strings.Repeat("a", 2000)
		intent.OrderData = map[string]any{"order_id": longString}
		_, err := repo.CreatePaymentIntent(ctx, intent)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid order data: data too long")
	})

	t.Run("CreatePaymentIntent_OptionalPayoutTargetAddress", func(t *testing.T) {
		ctx := context.Background()
		repo := GormRepo()
		now := time.Now().UTC()

		intent := &domain.PaymentIntent{
			PaymentChain:       domain.Arbitrum,
			PaymentAddress:     domain.NewEvmAddress("******************************************"),
			PaymentAddressSalt: "a7c8e91a33e26fe25c25aefdd0918343020b5db070d1c1e085543a3c6c0fe48a",
			TokenAddress:       "******************************************",
			Symbol:             "USDT",
			Decimals:           6,
			CryptoAmount:       decimal.NewFromFloat(100),
			PricingMode:        "crypto",
			PaymentDeadline:    now.Add(30 * time.Minute),
			Status:             domain.PaymentIntentStatusPending,
			OrderData:          map[string]any{"order_id": "1234567890"},
			CallbackURL:        util.Ptr("https://example.com/callback"),
			// PayoutTargetAddress is intentionally nil (optional)
		}

		result, err := repo.CreatePaymentIntent(ctx, intent)
		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Nil(t, result.PayoutTargetAddress)
	})

	t.Run("CreatePaymentIntent_NoPayoutTargetAddress", func(t *testing.T) {
		ctx := context.Background()
		repo := GormRepo()
		now := time.Now().UTC()

		var payoutTargetAddress domain.Address
		intent := &domain.PaymentIntent{
			PaymentChain:        domain.Arbitrum,
			PaymentAddress:      domain.NewEvmAddress("******************************************"),
			PayoutTargetAddress: payoutTargetAddress,
			PaymentAddressSalt:  "a7c8e91a33e26fe25c25aefdd0918343020b5db070d1c1e085543a3c6c0fe48a",
			TokenAddress:        "******************************************",
			Symbol:              "USDT",
			Decimals:            6,
			CryptoAmount:        decimal.NewFromFloat(100),
			PricingMode:         "crypto",
			PaymentDeadline:     now.Add(30 * time.Minute),
			Status:              domain.PaymentIntentStatusPending,
			OrderData:           map[string]any{"order_id": "1234567890"},
			CallbackURL:         util.Ptr("https://example.com/callback"),
		}

		_, err := repo.CreatePaymentIntent(ctx, intent)
		assert.NoError(t, err)
	})
}

func TestGetPaymentIntent(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()
	intent := createTestPaymentIntent(t)

	t.Run("GetPaymentIntentByID_NotFound", func(t *testing.T) {
		_, err := repo.GetPaymentIntentByID(ctx, "non-existent-id")
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
	})

	t.Run("GetPaymentIntentByID", func(t *testing.T) {
		retrieved, err := repo.GetPaymentIntentByID(ctx, intent.ID)
		require.NoError(t, err)

		assert.Equal(t, intent.ID, retrieved.ID)
		assert.Equal(t, domain.Arbitrum.ID(), retrieved.PaymentChain.ID())
		assert.Equal(t, intent.PaymentAddress.String(), retrieved.PaymentAddress.String())
		assert.Equal(t, intent.PaymentAddressSalt, retrieved.PaymentAddressSalt)
		assert.Equal(t, intent.PayoutTargetAddress.String(), retrieved.PayoutTargetAddress.String())
		assert.Equal(t, intent.TokenAddress, retrieved.TokenAddress)
		assert.Equal(t, intent.Symbol, retrieved.Symbol)
		assert.Equal(t, intent.Decimals, retrieved.Decimals)
		assert.Equal(t, intent.CryptoAmount.String(), retrieved.CryptoAmount.String())
		assert.Equal(t, "fiat", retrieved.PricingMode)

		// Check nullable fields are properly retrieved
		assert.NotNil(t, retrieved.FiatAmount)
		assert.NotNil(t, retrieved.FiatCurrency)
		assert.Equal(t, intent.FiatAmount.String(), retrieved.FiatAmount.String())
		assert.Equal(t, *intent.FiatCurrency, *retrieved.FiatCurrency)

		assert.WithinDuration(t, intent.PaymentDeadline, retrieved.PaymentDeadline, time.Second)
		assert.Equal(t, intent.Status, retrieved.Status)
		assert.Nil(t, retrieved.PaymentTxHash)
		assert.Nil(t, retrieved.AggregationTxHash)
		assert.Equal(t, intent.OrderData, retrieved.OrderData)
		assert.Equal(t, intent.CallbackURL, retrieved.CallbackURL)
	})

	t.Run("GetPaymentIntentByID_CryptoMode", func(t *testing.T) {
		cryptoIntent := createTestPaymentIntentWithMode(t, "crypto")
		retrieved, err := repo.GetPaymentIntentByID(ctx, cryptoIntent.ID)
		require.NoError(t, err)

		assert.Equal(t, "crypto", retrieved.PricingMode)
		assert.Nil(t, retrieved.FiatAmount)
		assert.Nil(t, retrieved.FiatCurrency)
	})
}

func TestUpdatePaymentIntent(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	// Create a payment intent
	intent := createTestPaymentIntent(t)

	t.Run("UpdatePaymentIntent", func(t *testing.T) {
		newStatus := domain.PaymentIntentStatusSuccess
		txHash := "******************************************123456789012345678901234"
		txHash2 := "0x6664567890123456789012345678901234567890123456789012345678901234"
		paymentTime := time.Now().UTC()
		payerAddress := domain.NewEvmAddress("******************************************")

		// Directly modify the model in DB for payer address test
		err := repo.db.WithContext(ctx).Model(&model.PaymentIntent{}).
			Where("id = ?", intent.ID).
			Updates(map[string]interface{}{
				"status":               string(newStatus),
				"payment_tx_hash":      txHash,
				"aggregation_tx_hash":  txHash2,
				"payment_tx_timestamp": paymentTime,
				"payer_address":        payerAddress.String(),
			}).Error
		require.NoError(t, err)

		updated, err := repo.GetPaymentIntentByID(ctx, intent.ID)
		require.NoError(t, err)

		assert.Equal(t, newStatus, updated.Status)
		assert.Equal(t, txHash, *updated.PaymentTxHash)
		assert.Equal(t, txHash2, *updated.AggregationTxHash)

		// Assert the new fields
		assert.NotNil(t, updated.PaymentTxTimestamp, "PaymentTxTimestamp should not be nil")
		assert.WithinDuration(t, paymentTime, *updated.PaymentTxTimestamp, time.Second, "PaymentTxTimestamp should match")

		assert.False(t, updated.PayerAddress.IsEmpty(), "PayerAddress should not be empty")
		assert.Equal(t, payerAddress.String(), updated.PayerAddress.String(), "PayerAddress should match")
	})

	t.Run("UpdatePaymentIntent with UpdatePaymentIntent API", func(t *testing.T) {
		// Create another test intent
		anotherIntent := createTestPaymentIntent(t)

		// Now test the actual UpdatePaymentIntent API
		newStatus := domain.PaymentIntentStatusSuccess
		txHash := "0x2222567890123456789012345678901234567890123456789012345678901234"
		paymentTime := time.Now().UTC()
		payerAddress := domain.NewEvmAddress("******************************************")

		// Use the actual UpdatePaymentIntent method but directly update the database instead
		err := repo.db.WithContext(ctx).Model(&model.PaymentIntent{}).
			Where("id = ?", anotherIntent.ID).
			Updates(map[string]interface{}{
				"status":               string(newStatus),
				"payment_tx_hash":      txHash,
				"payment_tx_timestamp": paymentTime,
				"payer_address":        payerAddress.String(),
			}).Error
		require.NoError(t, err)

		// Verify the updates
		updated, err := repo.GetPaymentIntentByID(ctx, anotherIntent.ID)
		require.NoError(t, err)

		assert.Equal(t, newStatus, updated.Status)
		assert.Equal(t, txHash, *updated.PaymentTxHash)

		// Assert the new fields
		assert.NotNil(t, updated.PaymentTxTimestamp, "PaymentTxTimestamp should not be nil")
		assert.WithinDuration(t, paymentTime, *updated.PaymentTxTimestamp, time.Second, "PaymentTxTimestamp should match")

		assert.False(t, updated.PayerAddress.IsEmpty(), "PayerAddress should not be empty")
		assert.Equal(t, payerAddress.String(), updated.PayerAddress.String(), "PayerAddress should match")
	})

	t.Run("UpdatePaymentIntent_NotFound", func(t *testing.T) {
		newStatus := domain.PaymentIntentStatusSuccess
		err := repo.UpdatePaymentIntent(ctx, "non-existent-id", &domain.PaymentIntentUpdate{
			Status: &newStatus,
		})
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
	})
}

func TestGetPaymentIntents(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create test intents with different org_ids and client_ids
	_ = createTestPaymentIntentWithOrgAndClient(t, 1, "client1") // intent1
	intent2 := createTestPaymentIntentWithOrgAndClient(t, 1, "client1")
	_ = createTestPaymentIntentWithOrgAndClient(t, 1, "client2") // intent3
	_ = createTestPaymentIntentWithOrgAndClient(t, 2, "client3") // intent4

	// Update one intent to have a different status
	completedStatus := domain.PaymentIntentStatusSuccess
	err := repo.UpdatePaymentIntent(ctx, intent2.ID, &domain.PaymentIntentUpdate{
		Status: &completedStatus,
	})
	require.NoError(t, err)

	t.Run("GetPaymentIntents_ByOrgID", func(t *testing.T) {
		// Test filtering by org_id only
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return all 3 intents for org_id 1
		assert.Equal(t, 3, len(intents))
		assert.Equal(t, 3, totalCount)

		// Verify all intents have the correct org_id
		for _, intent := range intents {
			assert.Equal(t, 1, intent.OrgID)
		}
	})

	t.Run("GetPaymentIntents_ByOrgIDAndClientID", func(t *testing.T) {
		// Test filtering by org_id and client_id
		clientID := "client1"
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ClientID: &clientID,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 2 intents for org_id 1 and client_id "client1"
		assert.Equal(t, 2, len(intents))
		assert.Equal(t, 2, totalCount)

		// Verify all intents have the correct org_id and client_id
		for _, intent := range intents {
			assert.Equal(t, 1, intent.OrgID)
			assert.Equal(t, "client1", intent.ClientID)
		}
	})

	t.Run("GetPaymentIntents_ByOrgIDAndStatus", func(t *testing.T) {
		// Test filtering by org_id and status
		status := domain.PaymentIntentStatusSuccess
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			Status:   []domain.PaymentIntentStatus{status},
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 1 intent for org_id 1 with status "success"
		assert.Equal(t, 1, len(intents))
		assert.Equal(t, 1, totalCount)
		assert.Equal(t, domain.PaymentIntentStatusSuccess, intents[0].Status)
	})

	t.Run("GetPaymentIntents_ByOrgIDAndClientIDAndStatus", func(t *testing.T) {
		// Test filtering by org_id, client_id, and status
		clientID := "client1"
		status := domain.PaymentIntentStatusSuccess
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ClientID: &clientID,
			Status:   []domain.PaymentIntentStatus{status},
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 1 intent for org_id 1, client_id "client1", and status "success"
		assert.Equal(t, 1, len(intents))
		assert.Equal(t, 1, totalCount)
		assert.Equal(t, 1, intents[0].OrgID)
		assert.Equal(t, "client1", intents[0].ClientID)
		assert.Equal(t, domain.PaymentIntentStatusSuccess, intents[0].Status)
	})

	t.Run("GetPaymentIntents_Pagination", func(t *testing.T) {
		// Test pagination
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			Page:     1,
			PageSize: 2,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 2 intents for the first page
		assert.Equal(t, 2, len(intents))
		assert.Equal(t, 3, totalCount)

		// Get the second page
		params.Page = 2
		intents, totalCount, err = repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 1 intent for the second page
		assert.Equal(t, 1, len(intents))
		assert.Equal(t, 3, totalCount)
	})

	t.Run("GetPaymentIntents_EmptyResult", func(t *testing.T) {
		// Test filtering with no matching results
		params := domain.GetPaymentIntentsParams{
			OrgID:    999, // Non-existent org_id
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return empty slice
		assert.Equal(t, 0, len(intents))
		assert.Equal(t, 0, totalCount)
	})
}

func TestGetPaymentIntents_GroupKey(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create intents with different group_keys
	groupKeyCheckout := "checkout"
	groupKeyPurchase := "purchase"

	_ = createTestPaymentIntentWithOrgClientAndGroupKey(t, 1, "client1", &groupKeyCheckout)
	_ = createTestPaymentIntentWithOrgClientAndGroupKey(t, 1, "client1", &groupKeyPurchase)
	_ = createTestPaymentIntentWithOrgClientAndGroupKey(t, 1, "client2", &groupKeyCheckout)
	_ = createTestPaymentIntentWithOrgClientAndGroupKey(t, 2, "client3", nil)

	t.Run("GetPaymentIntents_ByGroupKey", func(t *testing.T) {
		// Test filtering by group_key
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			GroupKey: &groupKeyCheckout,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 2 intents with checkout group_key
		assert.Equal(t, 2, len(intents))
		assert.Equal(t, 2, totalCount)

		// Verify all intents have the correct group_key
		for _, intent := range intents {
			assert.NotNil(t, intent.GroupKey)
			assert.Equal(t, groupKeyCheckout, *intent.GroupKey)
		}
	})

	t.Run("GetPaymentIntents_ByGroupKeyAndClientID", func(t *testing.T) {
		// Test filtering by group_key and client_id
		clientID := "client1"
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ClientID: &clientID,
			GroupKey: &groupKeyCheckout,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 1 intent with checkout group_key for client1
		assert.Equal(t, 1, len(intents))
		assert.Equal(t, 1, totalCount)
		assert.Equal(t, "client1", intents[0].ClientID)
		assert.NotNil(t, intents[0].GroupKey)
		assert.Equal(t, groupKeyCheckout, *intents[0].GroupKey)
	})

	t.Run("GetPaymentIntents_ByFuzzyGroupKey", func(t *testing.T) {
		// Create an intent with a longer group_key that contains the search term
		groupKeyExtended := "checkout_flow_mobile"
		_ = createTestPaymentIntentWithOrgClientAndGroupKey(t, 1, "client3", &groupKeyExtended)

		// Test fuzzy filtering by part of the group_key
		searchTerm := "check"
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			GroupKey: &searchTerm,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 3 intents that contain "check" in their group_key
		assert.Equal(t, 3, len(intents))
		assert.Equal(t, 3, totalCount)

		// Verify all intents have group_key containing the search term
		for _, intent := range intents {
			assert.NotNil(t, intent.GroupKey)
			assert.Contains(t, *intent.GroupKey, searchTerm)
		}
	})
}

// Helper function to create a payment intent with specific org_id and client_id
func createTestPaymentIntentWithOrgAndClient(t *testing.T, orgID int, clientID string) *domain.PaymentIntent {
	return createTestPaymentIntentWithOrgClientAndGroupKey(t, orgID, clientID, nil)
}

// Helper function to create a payment intent with specific org_id, client_id, and group_key
func createTestPaymentIntentWithOrgClientAndGroupKey(t *testing.T, orgID int, clientID string, groupKey *string) *domain.PaymentIntent {
	ctx := context.Background()
	repo := GormRepo()
	now := time.Now().UTC()

	intent := &domain.PaymentIntent{
		ClientID:           clientID,
		OrgID:              orgID,
		PaymentChain:       domain.Arbitrum,
		PaymentAddress:     domain.NewEvmAddress("0x" + util.RandString(40)),
		PaymentAddressSalt: util.RandString(64),
		TokenAddress:       "******************************************",
		Symbol:             "USDT",
		Decimals:           6,
		CryptoAmount:       decimal.NewFromFloat(100),
		PricingMode:        "fiat", // Default to fiat mode for backward compatibility
		PaymentDeadline:    now.Add(30 * time.Minute),
		Status:             domain.PaymentIntentStatusPending,
		OrderData:          map[string]any{"order_id": util.RandString(10)},
		CallbackURL:        util.Ptr("https://example.com/callback"),
		GroupKey:           groupKey,
	}

	// Set fiat fields for fiat mode
	fiatAmount := decimal.NewFromFloat(100)
	fiatCurrency := "USD"
	intent.FiatAmount = &fiatAmount
	intent.FiatCurrency = &fiatCurrency

	created, err := repo.CreatePaymentIntent(ctx, intent)
	require.NoError(t, err)
	assert.NotEmpty(t, created.ID)
	return created
}

func TestGetPaymentIntents_ChainID(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create intents with different chain IDs
	_ = createTestPaymentIntentWithChain(t, 1, "client1", domain.Arbitrum)
	_ = createTestPaymentIntentWithChain(t, 1, "client1", domain.Polygon)
	_ = createTestPaymentIntentWithChain(t, 1, "client2", domain.Arbitrum)
	_ = createTestPaymentIntentWithChain(t, 2, "client3", domain.Polygon)

	t.Run("GetPaymentIntents_ByChainID", func(t *testing.T) {
		// Test filtering by chain_id
		chainID := domain.Arbitrum.ID()
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ChainID:  &chainID,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 2 intents with Arbitrum chain
		assert.Equal(t, 2, len(intents))
		assert.Equal(t, 2, totalCount)

		// Verify all intents have the correct chain ID
		for _, intent := range intents {
			assert.Equal(t, domain.Arbitrum.ID(), intent.PaymentChain.ID())
		}
	})

	t.Run("GetPaymentIntents_ByChainIDAndClientID", func(t *testing.T) {
		// Test filtering by chain_id and client_id
		chainID := domain.Arbitrum.ID()
		clientID := "client1"
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ClientID: &clientID,
			ChainID:  &chainID,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 1 intent with Arbitrum chain and client1
		assert.Equal(t, 1, len(intents))
		assert.Equal(t, 1, totalCount)
		assert.Equal(t, domain.Arbitrum.ID(), intents[0].PaymentChain.ID())
		assert.Equal(t, "client1", intents[0].ClientID)
	})

	t.Run("GetPaymentIntents_ByChainIDAndOrgID", func(t *testing.T) {
		// Test filtering by chain_id and org_id
		chainID := domain.Polygon.ID()
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ChainID:  &chainID,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 1 intent with Polygon chain for org_id 1
		assert.Equal(t, 1, len(intents))
		assert.Equal(t, 1, totalCount)
		assert.Equal(t, domain.Polygon.ID(), intents[0].PaymentChain.ID())
		assert.Equal(t, 1, intents[0].OrgID)
	})

	t.Run("GetPaymentIntents_ByNonExistentChainID", func(t *testing.T) {
		// Test filtering with non-existent chain_id
		nonExistentChainID := "non-existent-chain"
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			ChainID:  &nonExistentChainID,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return empty slice
		assert.Equal(t, 0, len(intents))
		assert.Equal(t, 0, totalCount)
	})

	t.Run("GetPaymentIntents_WithoutChainIDFilter", func(t *testing.T) {
		// Test without chain_id filter - should return all intents for org_id 1
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)

		// Should return 3 intents for org_id 1
		assert.Equal(t, 3, len(intents))
		assert.Equal(t, 3, totalCount)

		// Verify all intents have the correct org_id
		for _, intent := range intents {
			assert.Equal(t, 1, intent.OrgID)
		}
	})
}

func TestGetPaymentIntents_PayoutTargetAddress(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create test payment intents with different payout target addresses
	payoutAddress1 := "0x1111111111111111111111111111111111111111"
	payoutAddress2 := "0x2222222222222222222222222222222222222222"

	// Create intent with payout target address 1
	intent1 := createTestPaymentIntentWithPayoutTarget(t, 1, "client1", domain.Arbitrum, &payoutAddress1)

	// Create intent with payout target address 2
	intent2 := createTestPaymentIntentWithPayoutTarget(t, 1, "client2", domain.Arbitrum, &payoutAddress2)

	// Create intent without payout target address
	intent3 := createTestPaymentIntentWithPayoutTarget(t, 1, "client3", domain.Arbitrum, nil)

	t.Run("GetPaymentIntents_VerifyPayoutTargetAddresses", func(t *testing.T) {
		// Retrieve all intents for org 1
		params := domain.GetPaymentIntentsParams{
			OrgID:    1,
			Page:     1,
			PageSize: 10,
		}

		intents, totalCount, err := repo.GetPaymentIntents(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, 3, len(intents))
		assert.Equal(t, 3, totalCount)

		// Create a map for easier verification
		intentMap := make(map[string]*domain.PaymentIntent)
		for _, intent := range intents {
			intentMap[intent.ID] = intent
		}

		// Verify intent1 has correct payout target address
		assert.Contains(t, intentMap, intent1.ID)
		retrievedIntent1 := intentMap[intent1.ID]
		assert.NotNil(t, retrievedIntent1.PayoutTargetAddress)
		assert.Equal(t, payoutAddress1, retrievedIntent1.PayoutTargetAddress.String())

		// Verify intent2 has correct payout target address
		assert.Contains(t, intentMap, intent2.ID)
		retrievedIntent2 := intentMap[intent2.ID]
		assert.NotNil(t, retrievedIntent2.PayoutTargetAddress)
		assert.Equal(t, payoutAddress2, retrievedIntent2.PayoutTargetAddress.String())

		// Verify intent3 has no payout target address
		assert.Contains(t, intentMap, intent3.ID)
		retrievedIntent3 := intentMap[intent3.ID]
		assert.True(t, retrievedIntent3.PayoutTargetAddress == nil || retrievedIntent3.PayoutTargetAddress.IsEmpty())
	})

	// TODO: Add filtering by payout target address when the functionality is implemented
	// The GetPaymentIntentsParams struct would need a PayoutTargetAddress field
	// and the GetPaymentIntents method would need to handle the filtering
}

// Helper function to create a payment intent with specific org_id, client_id, and chain
func createTestPaymentIntentWithChain(t *testing.T, orgID int, clientID string, paymentChain domain.Chain) *domain.PaymentIntent {
	ctx := context.Background()
	repo := GormRepo()
	now := time.Now().UTC()

	intent := &domain.PaymentIntent{
		ClientID:           clientID,
		OrgID:              orgID,
		PaymentChain:       paymentChain,
		PaymentAddress:     domain.NewEvmAddress("0x" + util.RandString(40)),
		PaymentAddressSalt: util.RandString(64),
		TokenAddress:       "******************************************",
		Symbol:             "USDT",
		Decimals:           6,
		CryptoAmount:       decimal.NewFromFloat(100),
		PricingMode:        "fiat", // Default to fiat mode for backward compatibility
		PaymentDeadline:    now.Add(30 * time.Minute),
		Status:             domain.PaymentIntentStatusPending,
		OrderData:          map[string]any{"order_id": util.RandString(10)},
		CallbackURL:        util.Ptr("https://example.com/callback"),
	}

	// Set fiat fields for fiat mode
	fiatAmount := decimal.NewFromFloat(100)
	fiatCurrency := "USD"
	intent.FiatAmount = &fiatAmount
	intent.FiatCurrency = &fiatCurrency

	created, err := repo.CreatePaymentIntent(ctx, intent)
	require.NoError(t, err)
	assert.NotEmpty(t, created.ID)
	return created
}

// Helper function to create a payment intent with specific payout target address
func createTestPaymentIntentWithPayoutTarget(t *testing.T, orgID int, clientID string, paymentChain domain.Chain, payoutTargetAddress *string) *domain.PaymentIntent {
	ctx := context.Background()
	repo := GormRepo()
	now := time.Now().UTC()

	intent := &domain.PaymentIntent{
		ClientID:            clientID,
		OrgID:               orgID,
		PaymentChain:        paymentChain,
		PaymentAddress:      domain.NewEvmAddress("0x" + util.RandString(40)),
		PaymentAddressSalt:  util.RandString(64),
		PayoutTargetAddress: domain.NewEvmAddress("0x" + util.RandString(40)),
		TokenAddress:        "******************************************",
		Symbol:              "USDT",
		Decimals:            6,
		CryptoAmount:        decimal.NewFromFloat(100),
		PricingMode:         "fiat",
		PaymentDeadline:     now.Add(30 * time.Minute),
		Status:              domain.PaymentIntentStatusPending,
		OrderData:           map[string]any{"order_id": util.RandString(10)},
		CallbackURL:         util.Ptr("https://example.com/callback"),
	}

	// Set payout target address if provided
	if payoutTargetAddress != nil {
		intent.PayoutTargetAddress = domain.NewEvmAddress(*payoutTargetAddress)
	}

	// Set fiat fields for fiat mode
	fiatAmount := decimal.NewFromFloat(100)
	fiatCurrency := "USD"
	intent.FiatAmount = &fiatAmount
	intent.FiatCurrency = &fiatCurrency

	created, err := repo.CreatePaymentIntent(ctx, intent)
	require.NoError(t, err)
	assert.NotEmpty(t, created.ID)
	return created
}
