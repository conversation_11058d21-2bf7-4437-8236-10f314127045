package model

import (
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
)

const TableNamePaymentIntents = "payment_intents"

type PaymentIntent struct {
	ID                     string                     `gorm:"column:id;type:varchar(36);primaryKey"`
	ClientID               string                     `gorm:"column:client_id;type:varchar(50);not null"`
	Payment<PERSON>hain           string                     `gorm:"column:payment_chain;type:varchar(20);not null"`
	PaymentAddress         string                     `gorm:"column:payment_address;type:varchar(60);not null"`
	PayerAddress           string                     `gorm:"column:payer_address;type:varchar(60)"`
	PayoutTargetAddress    *string                    `gorm:"column:payout_target_address;type:varchar(60);null"`
	PaymentAddressSalt     string                     `gorm:"column:payment_address_salt;type:varchar(64);not null"`
	TokenAddress           string                     `gorm:"column:token_address;type:varchar(100);not null"`
	Symbol                 string                     `gorm:"column:symbol;type:varchar(100);not null"`
	Decimals               uint                       `gorm:"column:decimals;type:smallint unsigned;not null"`
	CryptoAmount           decimal.Decimal            `gorm:"column:crypto_amount;type:decimal(36,18);not null"`
	ReceivedCryptoAmount   *decimal.Decimal           `gorm:"column:received_amount;type:decimal(36,18)"`
	AggregatedCryptoAmount *decimal.Decimal           `gorm:"column:aggregated_amount;type:decimal(36,18)"`
	RefundCryptoAmount     *decimal.Decimal           `gorm:"column:refund_amount;type:decimal(36,18)"`
	KGFeeAmount            *decimal.Decimal           `gorm:"column:kg_fee_amount;type:decimal(36,18);null"`
	FiatAmount             *decimal.Decimal           `gorm:"column:fiat_amount;type:decimal(36,18);null"`
	FiatCurrency           *string                    `gorm:"column:fiat_currency;type:varchar(10);null"`
	CryptoPrice            *decimal.Decimal           `gorm:"column:crypto_price;type:decimal(20,8);null"`
	PricingMode            string                     `gorm:"column:pricing_mode;type:enum('fiat','crypto');not null;default:'fiat'"`
	PaymentDeadline        time.Time                  `gorm:"column:payment_deadline;type:timestamp;not null"`
	Status                 domain.PaymentIntentStatus `gorm:"column:status;type:enum('pending','success','expired','insufficient_refunded','insufficient_not_refunded','cancelled');not null;default:'pending'"`
	PaymentTxHash          *string                    `gorm:"column:payment_tx_hash;type:varchar(100)"`
	PaymentTxTimestamp     *time.Time                 `gorm:"column:payment_tx_timestamp;type:timestamp"`
	AggregationTxHash      *string                    `gorm:"column:aggregation_tx_hash;type:varchar(100)"`
	AggregationTxTimestamp *time.Time                 `gorm:"column:aggregation_tx_timestamp;type:timestamp"`
	FinalizedTimestamp     *time.Time                 `gorm:"column:finalized_timestamp;type:timestamp"`
	RefundTxHash           *string                    `gorm:"column:refund_tx_hash;type:varchar(100)"`
	CancelledAt            *time.Time                 `gorm:"column:cancelled_at;type:timestamp"`
	CancellationReason     *string                    `gorm:"column:cancellation_reason;type:varchar(255)"`
	OrderData              string                     `gorm:"column:order_data;type:json"`
	CallbackURL            *string                    `gorm:"column:callback_url;type:varchar(1000)"`
	GroupKey               *string                    `gorm:"column:group_key;type:varchar(100)"`
	OrgID                  int                        `gorm:"column:org_id;type:int"`
	CreatedAt              time.Time                  `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP;not null"`
	UpdatedAt              time.Time                  `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP;not null;autoUpdateTime"`
}

func (PaymentIntent) TableName() string {
	return TableNamePaymentIntents
}
